"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'serviceConf';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        rating: { type: Number },
        introduction: { type: String, trim: true },
        audio: { type: String, trim: true },
        audioTime: { type: Number },
        hours: { type: [[Date]] },
        // validDate: {type: [[Date]]}, // 有效日期, 当前用户的一天的开始时间, 格式: [[start, end], ...]
        holiday: { type: [[Date]] },
        enable: { type: Schema.Types.Mixed },
        serviceRoles: { type: [String], enum: Agl.ServiceRoles, default: Agl.ServiceRoles },
        fans: { type: Number, default: 0 },
        // 以下字段用于索引，数据在更新的时候自动生成
        hoursIndex: { type: [[String]] },
        hoursMax: { type: Number },
        /*
        按服务包的认证项 认证通过时间来排序 #4455
        认证项: 认证通过时间
        */
        sort: { type: Schema.Types.Mixed },
        // 滞后显示：若老师terminate/cancel了超过1/3的被预约辅导课
        lag: { type: Boolean, default: false },
        lastAuth: { type: Date },
        count: {
            rate: { type: Number, default: 0 },
            rating: { type: Number, default: 0 },
            accident: { type: Number, default: 0 },
            booking: { type: Number, default: 0 },
            cancel: { type: Number, default: 0 },
            terminate: { type: Number, default: 0 }, //
        },
        country: { type: String, trim: true },
        city: { type: String, trim: true },
        address: { type: String, trim: true },
        place_id: { type: String, trim: true },
        location: {
            type: {
                type: String,
                enum: ['Point'],
            },
            coordinates: {
                type: [Number],
            },
        },
        serviceRadius: { type: Number },
        attachmentsAddress: {
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
        attachmentsVetting: {
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
        vettingDate: { type: Date },
        status: { type: Number, default: 0 },
        vettingReminder: { type: Boolean, default: false },
        vettingExpiredReminder: { type: Boolean, default: false },
        feedback: {
            // 留言反馈
            message: { type: String },
            date: { type: Date },
            read: { type: Boolean, default: false },
            reply: { type: String },
            replyDate: { type: Date },
            replyRead: { type: Boolean, default: false }, // read status
        },
        reason: { type: String, trim: true },
    }, {
        timestamps: true,
    });
    schema.index({ location: '2dsphere' }, { sparse: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
