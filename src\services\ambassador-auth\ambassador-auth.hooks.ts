import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
import hook from '../../hook'

export default {
  before: {
    all: [authenticate('jwt')],
    find: [hook.queryDate('updatedAt')],
    get: [],
    create: [
      (d: HookContext) => {
        d.data.uid = d.params.user?._id
        if (d.params.user?.roles.includes('student')) {
          d.data.role = 'student'
        } else {
          d.data.role = 'teacher'
        }
      },
    ],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        for (const o of d.result.data) {
          await d.service.ext(o)
        }
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        await d.service.ext(d.result)
      },
    ],
    create: [],
    update: [],
    patch: [
      async (d: HookContext) => {
        let {status} = d.data
        if (status == 2) {
          await d.service.send(d.result, 'SchoolAmbassadorApproved', d.params)
        }
        if (status == -1) {
          await d.service.send(d.result, 'SchoolAmbassadorRejected', d.params)
        }
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
