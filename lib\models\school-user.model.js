"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'schoolUser';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String },
        school: { type: String, required: true, trim: true },
        email: { type: String, required: true, trim: true },
        name: { type: [String], trim: true },
        nickname: { type: String, trim: true },
        avatar: { type: String, trim: true },
        dob: { type: String, trim: true },
        role: { type: [String], trim: true },
        class: { type: [String], trim: true },
        head: { type: [String], trim: true },
        subject: { type: [String], trim: true },
        subjectGrade: { type: Schema.Types.Mixed },
        status: { type: Number, default: 0 },
        del: { type: Boolean, default: false },
        closeAlert: { type: Boolean, default: false }, // 关闭显示弹框提示,进入特殊学校
    }, {
        timestamps: true,
    });
    schema.index({ school: 1, email: -1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
