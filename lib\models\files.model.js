"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'files';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        _id: { type: String, required: true },
        ext: { type: String, required: true },
        pre: { type: String },
        mime: { type: String, required: true },
        size: { type: Number, required: true },
        ref: [{
                _id: { type: Number },
                r: { type: String }, // 关联数据
            }],
        title: { type: Schema.Types.Mixed },
        public: { type: Boolean, default: true }
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
