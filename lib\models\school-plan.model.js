"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'schoolPlan';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        name: { type: String, index: true, trim: true },
        logo: { type: String, trim: true },
        country: { type: String, index: true, trim: true },
        city: { type: String, index: true, trim: true },
        address: { type: String, trim: true },
        phone: { type: [String], trim: true },
        start: { type: Date, index: true },
        end: { type: Date, index: true },
        status: { type: Number, index: true, default: 0 },
        teacher: { type: Number, default: 1 },
        student: { type: Number, default: 10 },
        count: {
            teacher: { type: Number, default: 0 },
            student: { type: Number, default: 0 },
            grade: { type: Number, default: 0 },
            class: { type: Number, default: 0 },
        },
        space: { type: Number, default: 1000 * 1024 * 1024 },
        pilot: { type: Boolean, default: false },
        personal: { type: Boolean, default: false },
        commissionEnable: { type: Boolean, default: false },
        contact: { type: String },
        inviter: { type: String, trim: true },
        pipelineEnable: { type: Boolean, default: false },
        pipelineStatus: { type: Number, default: 0 },
        pipelineAt: { type: Date },
        contentProviderEnable: { type: Boolean, default: false },
        contentProviderStatus: { type: Number, default: 0 },
        contentProviderAt: { type: Date },
        attachmentsLogo: {
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
        attachmentsCurriculum: [
            {
                type: { type: String, trim: true, enum: ['Certificate', 'Foundation', 'Master', 'Bachelor', 'Diploma'] },
                subject: { type: String, trim: true },
                attachments: {
                    filename: { type: String, trim: true },
                    mime: { type: String, trim: true },
                    hash: { type: String, trim: true },
                    date: { type: Date },
                    type: { type: String, trim: true },
                    size: { type: Number }, // 文件大小
                },
            },
        ],
        feedback: {
            // 留言反馈
            message: { type: String },
            date: { type: Date },
            read: { type: Boolean, default: false },
            reply: { type: String },
            replyDate: { type: Date },
            replyRead: { type: Boolean, default: false }, // read status
        },
        balance: { type: Number, default: 0 }, // 佣金+收入 美分
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
