// service-task-section.schema.ts
import {Schema} from 'mongoose'

export const SectionSchema = new Schema(
  {
    name: {type: String, trim: true, required: true},
    prompt: {type: String, trim: true},
    salesPrice: {type: Number, default: 0},
    costPrice: {type: Number, default: 0},
    serviceTaskId: {type: String, trim: true}, // service-pack._id
  },
  {
    timestamps: true,
    _id: true,
  }
)
