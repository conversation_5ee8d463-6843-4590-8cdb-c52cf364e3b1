"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'serviceAuthMessage';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        rid: { type: String, required: true },
        message: { type: String },
        read: { type: Boolean, default: false },
        isAdmin: { type: Boolean, default: false },
        type: { type: String, default: 'service-auth', enum: ['service-auth', 'service-conf', 'school-plan'] },
        attachments: {
            // 图片/视频证据
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
