"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'curriculumSubject';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        code: { type: String, required: true, trim: true, unique: true },
        name: { type: String, required: true, trim: true },
        child: [{
                name: { type: String, trim: true },
                grade: { type: [Number] },
                mark: { type: String, trim: true },
                child: [{
                        name: { type: String, trim: true },
                        grade: { type: [Number] },
                        mark: { type: String, trim: true },
                        child: [{
                                name: { type: String, trim: true },
                                grade: { type: [Number] },
                                mark: { type: String, trim: true },
                                child: [{
                                        name: { type: String, trim: true },
                                        grade: { type: [Number] },
                                        mark: { type: String, trim: true },
                                    }]
                            }]
                    }]
            }],
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
