"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const got = require('got');
// const callUri = '/classcipe/thirdLogin/zoom/callback'
const callUri = '/fio/zoom/callback';
function default_1(app) {
    const zoom = app.get('zoom');
    // app.get('/zoom/out', async (req: Request, res: Response) => {
    //   const {uid}: any = req.params.user
    //   await knexj('sys_third_account').where({third_type: 'zoom', sys_user_id: uid}).del()
    //   res.json()
    // })
    app.get('/zoom/auth', async (req, res) => {
        let host = !isDev ? 'api.classcipe.com' : req.hostname;
        if (host === 'api')
            host = 'dev.classcipe.com';
        const { uid } = req.query;
        const ubj = new URL('https://zoom.us/oauth/authorize?response_type=code');
        ubj.searchParams.set('client_id', zoom.id);
        ubj.searchParams.set('redirect_uri', `https://${host}${callUri}`);
        ubj.searchParams.set('state', JSON.stringify({ role: 'teacher', uid }));
        logger_1.default.info(ubj.href);
        res.redirect(ubj.href);
    });
    app.get('/zoom/callback', async (req, res) => {
        const host = !isDev ? 'api.classcipe.com' : req.hostname;
        const { code, state } = req.query;
        if (!code || !state)
            return res.json({ message: 'error code' });
        const { uid, url } = JSON.parse(state);
        const ubj = new URL('https://zoom.us/oauth/token?grant_type=authorization_code');
        ubj.searchParams.set('code', code);
        ubj.searchParams.set('redirect_uri', `https://${host}${callUri}`);
        // const user = await app.service('users').Model.findById(uid)
        const Authorization = 'Basic ' + Acan.base64encode(`${zoom.id}:${zoom.secret}`);
        const rs = (await got.post(ubj.href, {
            headers: { Authorization, 'Content-Type': 'application/x-www-form-urlencoded' },
            json: true,
        })).body;
        logger_1.default.warn(rs, 'zoom auth:');
        if (rs.error)
            return res.json(rs);
        const headers = { Authorization: 'Bearer ' + rs.access_token };
        const info = (await got(`https://api.zoom.us/v2/users/me`, { headers, json: true })).body;
        const setting = (await got(`https://api.zoom.us/v2/users/me/settings`, { headers, json: true })).body;
        const payload = jsonwebtoken_1.default.decode(rs.access_token);
        if (!payload.uid)
            return res.json({ message: 'error code' });
        await app.service('users').Model.updateOne({ _id: uid }, { $set: { zoom: payload.uid } });
        const post = {
            token: rs.access_token,
            rt: rs.refresh_token,
            exp: payload.exp,
            email: info.email,
            ext: setting.feature,
        };
        await app.service('user-token').Model.updateOne({ type: 'zoom', sub: payload.uid }, post, { upsert: true });
        logger_1.default.warn('zoom auth:', post, rs);
        // return res.json({ ok: 1, uid, nickname: user.nickname, email: user.email })
        res.send('<!DOCTYPE html><html lang="en"><script>window.close()</script></html>');
    });
}
exports.default = default_1;
