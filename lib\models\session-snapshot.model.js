"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'sessionSnapshot';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String, required: true },
        createBy: { type: String, default: 'user', enum: ['user', 'cron'] },
        students: { type: Schema.Types.Mixed },
        members: { type: Schema.Types.Mixed },
        teachers: { type: Schema.Types.Mixed },
        pages: { type: Schema.Types.Mixed },
        questions: { type: Schema.Types.Mixed },
        materials: { type: Schema.Types.Mixed },
        comment: { type: String, trim: true },
        response: { type: [Schema.Types.Mixed] },
        comments: { type: [Schema.Types.Mixed] },
        // 自动计算出课堂统计数据
        stats: [
            {
                page: { type: String },
                question: { type: String },
                answer: { type: Number },
                options: { type: [Number] }, // 选择题，各选项选择人数
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
