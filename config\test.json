{"host": "0.0.0.0", "port": 4000, "public": "../public/", "paginate": {"default": 10, "max": 2000}, "nedb": "../data", "api3Url": "http://testapi3:3000", "mqttUrl": "mqtt://***********", "redisUrl": "redis://:@redis:6379/1", "redisOld": "redis://:@redis:6379/0", "redisSync": "redis://:@redis:6379/3", "mongodb": "***********************************************************************", "mysql": "mysql://api:<EMAIL>:3306/newzealand", "mysqlj": "mysql://api:<EMAIL>:3306/classcipe", "cdnUrl": "https://d19jvc3fkrigab.cloudfront.net/", "s3": {"accessKeyId": "********************", "secretAccessKey": "MZ3kq+XlPYzeG1Kj3n8dEi/9SjWj9eP7N46ZKwT+", "region": "ap-southeast-2"}, "googleAuth": ["337694010706-actv4k6bli7c4iknu6ak50gkqhk318ti.apps.googleusercontent.com", "y1trMdxHMa_L7eGQMh_PgCWm", "https://classcipe.com/fio/auth.html"], "zoom": {"id": "Xim9Q3SUT46lTYjbSiEKZQ", "secret": "ttERV0fg36U6d6Xce2CWGrd184PsBzmT"}, "certbot": {"key": "********************", "secret": "53IiBAJghqxK73rLkoU/Fk0rHkgFanlz6qs9gbj0"}, "authentication": {"entity": "user", "service": "users", "secret": "classcipe-!@#", "authStrategies": ["jwt", "local", "sync", "guest", "mobile"], "jwtOptions": {"header": {"typ": "access"}, "audience": "https://classcipe.com", "subject": "", "issuer": "feathers", "algorithm": "HS256", "expiresIn": "3d"}, "local": {"entity": "user", "usernameField": "email", "passwordField": "password"}, "mobile": {"entity": "user", "usernameField": "mobile", "passwordField": "password"}, "sync": {"entity": "user", "usernameField": "email"}, "guest": {"entity": "user", "usernameField": "\\username"}, "oauth": {"redirect": "/", "auth0": {"key": "<auth0 oauth key>", "secret": "<auth0 oauth secret>", "subdomain": "<auth0 subdomain>", "scope": ["profile", "openid", "email"]}, "google": {"key": "<google oauth key>", "secret": "<google oauth secret>", "scope": ["email", "profile", "openid"]}}}}