"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'template';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        id: { type: String, required: true, trim: true },
        cover: { type: String, trim: true },
        unitName: { type: String, trim: true },
        type: { type: String, required: true, enum: Agl.questionsTypes },
        tab: { type: String, required: true, enum: Agl.templateTab },
        category: { type: String, required: false },
        default: { type: Boolean, default: false }, // 每个tab下可以设置为默认值 https://github.com/zran-nz/bug/issues/5560
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
