"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'outlines';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, sparse: true },
        task: { type: String, sparse: true, unique: true, trim: true },
        id: { type: String, sparse: true, trim: true },
        subjects: [{ type: String, trim: true }],
        years: [{ type: String, trim: true }],
        list: [{
                md5: { type: String, trim: true },
                code: { type: String, trim: true },
                text: { type: String, required: true, trim: true },
                path: { type: String, required: true, trim: true },
                bloom: { type: Number },
                dimension: { type: Number },
                verb: [{ type: String }],
                tags: [{ type: String }], // knowledge tags
            }]
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
