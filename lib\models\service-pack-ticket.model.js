"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackTicket';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        // code: {type: String}, // 编号
        uid: { type: String },
        school: { type: String, required: true },
        servicePremium: { type: String, required: true },
        order: { type: String, trim: true },
        serviceData: {
            type: [
                {
                    servicePack: { type: String },
                    cash: { type: Number, default: 0 },
                    point: { type: Number, default: 0 },
                    gift: { type: Number, default: 0 },
                    cashOrigin: { type: Number, default: 0 },
                    pointOrigin: { type: Number, default: 0 },
                    giftOrigin: { type: Number, default: 0 }, // 赠送数量
                },
            ],
        },
        refund: { type: Boolean, default: false }, // 已退款
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
