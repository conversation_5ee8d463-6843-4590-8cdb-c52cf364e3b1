"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'schoolTerm';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true, trim: true },
        year: { type: String, required: true },
        title: { type: String, required: true, trim: true },
        start: { type: Date, required: true },
        end: { type: Date, required: true },
        time: { type: Number, default: 2700 },
        break: { type: Number, default: 600 },
        block: [
            {
                week: { type: Number },
                start: { type: String, required: true },
                end: { type: String, required: true },
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
