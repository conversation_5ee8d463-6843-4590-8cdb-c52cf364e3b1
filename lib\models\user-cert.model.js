"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'userCert';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, sparse: true },
        school: { type: String, sparse: true },
        code: { type: String, trim: true, required: true },
        reason: { type: String, trim: true },
        ext: {
            inService: { type: Boolean, default: false },
            schoolName: { type: String, trim: true },
            year: { type: String, trim: true },
        },
        pics: [
            {
                _id: { type: String, trim: true, required: true },
                ext: { type: String, trim: true },
                code: { type: String, trim: true, required: true }, // pic code
            },
        ],
        status: { type: Number, default: 0 }, // 0: default, 1: applying, 2: success, -1: faild
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
