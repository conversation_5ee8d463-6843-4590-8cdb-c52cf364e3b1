"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackUserLogs';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        packUser: { type: String, required: true },
        times: { type: Number, required: true },
        compensation: { type: Number },
        type: { type: String, enum: Agl.ServicePackUserType },
        remaining: { type: Number },
        expireSoon: { type: Date },
        start: { type: Date },
        name: { type: String },
        servicer: {
            uid: { type: String },
            avatar: { type: String },
            name: { type: [String] }, // users.name
        },
        packUserData: [
            {
                _id: { type: String, required: true },
                payMethod: { type: String, trim: true },
                order: { type: String, trim: true },
                expired: { type: Date }, // 过期
            },
        ],
        session: { type: String },
        packUserSnapshot: { type: Schema.Types.Mixed }, // service-pack-user 快照
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
