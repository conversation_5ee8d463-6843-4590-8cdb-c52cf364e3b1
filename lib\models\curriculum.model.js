"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'curriculum';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        code: { type: String, required: true },
        name: { type: String, required: true, trim: true },
        enable: { type: Boolean, default: false },
        topic: { type: Boolean, default: false },
        standard: { type: Boolean, default: false },
        grade: [{ type: String, trim: true }],
        year: [{ type: String, trim: true }],
        subjects: [{
                _id: { type: String, trim: true },
                name: { type: String, trim: true },
                enable: { type: Boolean, default: false },
                child: [{
                        _id: { type: String, trim: true },
                        enable: { type: Boolean, default: false },
                        name: { type: String, trim: true },
                        child: [{
                                _id: { type: String, trim: true },
                                enable: { type: <PERSON><PERSON><PERSON>, default: false },
                                name: { type: String, trim: true }
                            }]
                    }]
            }],
    }, {
        timestamps: true
    });
    schema.index({ uid: 1, code: -1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
