import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class Message extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async extUser(one: any, params?: Params) {
    one.userInfo = await this.app.service('users').Model.findOne({_id: one.uid})
  }

  async send(doc: any, params: Params): Promise<any> {
    const {type, rid, role, message} = doc
    let sender: any = await this.app.service('users').Model.findOne({_id: params.user?._id})
    // service-auth-premium
    if (type == 'service-auth-premium') {
      let business: any = await this.app.service('service-auth').Model.findOne({_id: rid})
      let name = await this.app.service('service-auth').getAuthName(business)
      if (role == 'admin') {
        let user: any = await this.app.service('users').Model.findOne({_id: business.uid})
        let url = `${SiteUrl}/v2/account/teacher/auth/edit/verification`
        return await this.app.service('notice-tpl').mailto(
          'MessageToUserRegardingPremiumContentVerification',
          user,
          {
            username: user.name.join(' '),
            name: name.slice(0, name.length - 1).join('-'),
            service_type: name[name.length - 1],
            url,
          },
          params.user?._id
        )
      }
      if (role == 'user') {
        let user: any
        if (business.follower) {
          user = await this.app.service('users').Model.findOne({_id: business.follower})
        } else {
          user = await this.app.service('users').Model.findOne({email: '<EMAIL>'})
        }
        let url = `${SiteUrl}/v2/sys/premium-content?tab=1&currentVerification=academic`
        return await this.app.service('notice-tpl').mailto(
          'MessageToClasscipeRegardingPremiumContentVerification',
          user,
          {
            username: user.name.join(' '),
            name: name.slice(0, name.length - 1).join('-'),
            service_type: name[name.length - 1],
            url,
          },
          params.user?._id
        )
      }
    }

    //Associted task
    if (type == 'associated-task') {
      let business: any = await this.app.service('service-pack-user').Model.findOne({_id: rid})

      const senderId = params.user?._id
      const bookerId = business.uid
      const serviceProviderId = business.associatedServicer?.uid

      if (senderId === bookerId) {
        // Message sent by booker/student - send email to teacher/service provider
        if (serviceProviderId) {
          let teacher: any = await this.app.service('users').Model.findOne({_id: serviceProviderId})
          let url = `${SiteUrl}/v2/home/<USER>
          return await this.app.service('notice-tpl').mailto(
            'associatedTaskMessageIncoming(sendByBooker)',
            teacher,
            {
              username: teacher?.nickname,
              service_name: business.snapshot.name,
              url: `${SiteUrl}/v2/home/<USER>
            },
            params.user?._id
          )
        }
      } else if (senderId === serviceProviderId) {
        // Message sent by service provider/teacher - send email to booker/student
        let student: any = await this.app.service('users').Model.findOne({_id: bookerId})
        let url = `${SiteUrl}/v2/study/purchased?tab=myAssociatedTask&subtab=ongoing`
        return await this.app.service('notice-tpl').mailto(
          'associatedTaskMessageIncoming(sendByProvider)',
          student,
          {
            username: student?.nickname,
            service_name: business.snapshot.name,
            url: `${SiteUrl}/v2/study/purchased?tab=myAssociatedTask&subtab=ongoing`,
          },
          params.user?._id
        )
      }
    }

    // service-pack-apply
    if (type == 'service-pack-apply') {
      let business: any = await this.app.service('service-pack-apply').Model.findOne({_id: rid})
      let student: any = await this.app.service('users').Model.findOne({_id: business.uid})
      let servicePack: any = await this.app.service('service-pack').Model.findOne({_id: business.servicePack})
      if (role == 'admin') {
        if (business.sharedSchool) {
          let admins = await this.app.service('school-user').Model.find({school: business.sharedSchool, role: 'admin'})
          for (let i = 0; i < admins.length; i++) {
            const admin: any = admins[i]
            let url = `${SiteUrl}/v2/premcpack/applicationTrackDetail/${business.servicePack}`
            return await this.app.service('notice-tpl').mailto(
              'MessageToUserRegardingPremiumLectureApplicationA',
              admin.email,
              {
                username: admin.name.join(' '),
                studentname: student.name.join(' '),
                name: servicePack.name,
                url,
              },
              params.user?._id
            )
          }
        } else {
          let url = `${SiteUrl}/v2/premcpack/myApplication`
          return await this.app.service('notice-tpl').mailto(
            'MessageToUserRegardingPremiumLectureApplicationB',
            student,
            {
              username: student.name.join(' '),
              name: servicePack.name,
              message,
              url,
            },
            params.user?._id
          )
        }
      }
      if (role == 'user') {
        let user: any
        if (business.follower) {
          user = await this.app.service('users').Model.findOne({_id: business.follower})
        } else {
          user = await this.app.service('users').Model.findOne({email: '<EMAIL>'})
        }
        let url = `${SiteUrl}/v2/premcpack/enroll/${business.servicePack}`
        return await this.app.service('notice-tpl').mailto(
          'MessageToClasscipeRegardingPremiumLectureApplication',
          user,
          {
            username: user.name.join(' '),
            name: servicePack.name,
            url,
          },
          params.user?._id
        )
      }
    }
    // school-plan
    if (type == 'school-plan') {
      let business: any = await this.app.service('school-plan').Model.findOne({_id: rid})
      if (role == 'user') {
        let user: any = await this.app.service('users').Model.findOne({email: '<EMAIL>'})
        let url = `${SiteUrl}/v2/sys/school/${rid}`
        return await this.app.service('notice-tpl').mailto(
          'MessageToClasscipeRegardingContentProviderSetting',
          user,
          {
            username: user.name.join(' '),
            schoolname: business.name,
            url,
          },
          params.user?._id
        )
      }
      if (role == 'admin') {
        let admins = await this.app.service('school-user').Model.find({school: rid, role: 'admin'})
        let url = `${SiteUrl}v2/account/info`
        if (business.contentProviderStatus != 2) {
          url = ''
        }
        for (let i = 0; i < admins.length; i++) {
          const admin: any = admins[i]
          return await this.app.service('notice-tpl').mailto(
            'MessageToUserRegardingContentProviderSetting',
            admin.email,
            {
              username: admin.name.join(' '),
              url,
            },
            params.user?._id
          )
        }
      }
    }

    // service-auth
    if (type == 'service-auth') {
      let business: any = await this.app.service('service-auth').Model.findOne({_id: rid})
      let name = await this.app.service('service-auth').getAuthName(business)
      if (role == 'user') {
        let user: any = await this.app.service('users').Model.findOne({email: '<EMAIL>'})
        let url = `${SiteUrl}/v2/sys/teacher-verification/${rid}`
        return await this.app.service('notice-tpl').mailto(
          'MessageToClasscipeRegardingServiceVerification',
          user,
          {
            username: user.name.join(' '),
            name: name.slice(0, name.length - 1).join('-'),
            service_type: name[name.length - 1],
            url,
          },
          params.user?._id
        )
      }
      if (role == 'admin') {
        let user: any = await this.app.service('users').Model.findOne({_id: business.uid})
        let url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${rid}`
        return await this.app.service('notice-tpl').mailto(
          'MessageToUserRegardingServiceVerification',
          user,
          {
            username: user.name.join(' '),
            name: name.slice(0, name.length - 1).join('-'),
            service_type: name[name.length - 1],
            url,
          },
          params.user?._id
        )
      }
    }
    // classes 用announcement
    // if (type == 'classes') {
    //   if (role == 'admin') {
    //     let business: any = await this.app.service('classes').Model.findOne({_id: rid})
    //     let students = await this.app.service('students').Model.find({class: rid})
    //     let schoolPlan: any = await this.app.service('school-plan').Model.findOne({_id: business.school})

    //     for (let i = 0; i < students.length; i++) {
    //       const student: any = students[i]
    //       let url = ``
    //       return await this.app.service('notice-tpl').mailto(
    //         'MessageToAllRegardingClassAnnouncement',
    //         student.email,
    //         {
    //           username: student.name.join(' '),
    //           teachername: sender.name.join(' '),
    //           classname: business.name,
    //           schoolname: schoolPlan.name,
    //           url,
    //         },
    //         params.user?._id
    //       )
    //     }
    //   }
    // }
    // cloud-room

    // session-takeaway
    if (type == 'session-takeaway') {
      let session: any = await this.app.service('session').Model.findOne({_id: rid})
      let teacher: any = await this.app.service('users').Model.findOne({_id: session.uid})
      if (role == 'admin') {
        let url = ``
        return await this.app.service('notice-tpl').mailto(
          'MessageToTakeawayEvaluator',
          teacher,
          {
            username: teacher.name.join(' '),
            name: session.name,
            url,
          },
          params.user?._id
        )
      }
    }
    // prompts unit self-study
    if (type == 'prompts' || type == 'unit' || type == 'self-study') {
      let business: any = {}
      if (type == 'prompts') {
        business = await this.app.service('prompts').Model.findOne({_id: rid})
      } else if (type == 'unit') {
        business = await this.app.service('unit').Model.findOne({_id: rid})
      }
      // else if (type == 'self-study') {
      //   business = await this.app.service('session').Model.findOne({_id: rid})
      // }
      let author: any = await this.app.service('users').Model.findOne({_id: business.uid})
      if (role == 'admin') {
        let url = ``
        return await this.app.service('notice-tpl').mailto(
          'MessageToAuthorRegardingThePublishedContent',
          author,
          {
            username: author.name.join(' '),
            name: business?.name ? `${business?.name} of Teaching resource` : 'of Prompts',
            url,
          },
          params.user?._id
        )
      }
      if (role == 'user') {
        let user: any = await this.app.service('users').Model.findOne({email: '<EMAIL>'})
        let url = ``
        return await this.app.service('notice-tpl').mailto(
          'MessageToClasscipeRegardingThePublishedContent',
          user,
          {
            username: user.name.join(' '),
            author: author.name.join(' '),
            name: business?.name ? `${business?.name} of Teaching resource` : 'of Prompts',
            url,
          },
          params.user?._id
        )
      }
    }
  }
}
