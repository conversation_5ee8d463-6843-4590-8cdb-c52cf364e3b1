"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const db = app.get('knexClientj');
    const tableName = 'ta_templates';
    db.schema.hasTable(tableName).then(exists => {
        if (!exists) {
            db.schema.createTable(tableName, table => {
                table.increments('id');
                table.string('text');
            })
                .then(() => console.log(`Created ${tableName} table`))
                .catch(e => console.error(`Error creating ${tableName} table`, e));
        }
    });
    return db;
}
exports.default = default_1;
