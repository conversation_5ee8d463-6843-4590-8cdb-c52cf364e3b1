// bonus-setting-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'bonusSetting'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      tab: {type: String, enum: ['earn', 'claim'], default: 'earn'}, //类目1 earn:获取 claim:使用/提现
      category: {
        type: String,
        required: true,
        enum: ['ambassador_school'],
      },
      accumulated: {type: Number, required: true}, //累计门槛
      mode: {type: String, required: true, default: 'percentage', enum: ['fixed', 'percentage']}, //奖励模式 固定数值/按比例
      value: {type: Number, required: true}, //固定数值;按比例
    },
    {
      timestamps: true,
    }
  )

  schema.index({tab: 1, category: 1}, {unique: true})
  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
