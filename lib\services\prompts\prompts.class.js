"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prompts = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const errors_1 = require("@feathersjs/errors");
class Prompts extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
        this.selectList = [];
    }
    // 生成快照
    async snapshot({ _id }, params) {
        const snapshot = await this.app.service('prompts').Model.findById(_id);
        if (!snapshot)
            return Promise.reject(new errors_1.NotFound());
        return Acan.clone(snapshot);
    }
    async extUser(one, params) {
        one.userInfo = await this.app.service('users').uidToInfo(one.uid);
    }
}
exports.Prompts = Prompts;
