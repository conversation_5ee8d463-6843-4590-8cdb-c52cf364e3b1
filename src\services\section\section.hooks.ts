import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
import logger from '../../logger'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [hook.toClass],
    create: [],
    update: [],
    patch: [],
    remove: [hook.sysQuery()],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        for (const o of d.result.data) {
          logger.log('dkkffll', o)
          await d.service.extUser(o)
        }
        return d
      },
    ],
    get: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result

        if (connection && result && result._id) {
          const sectionId = result._id.toString()
          context.app.channel(`section/${sectionId}`).join(connection)
        }

        return context
      },
    ],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
