"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'pointSetting';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        tab: { type: String, enum: ['earn', 'claim'], default: 'earn' },
        category: {
            type: String,
            required: true,
            enum: [
                'invite',
                'verify',
                'unit',
                'service',
                'session',
                'self_study',
                'service_premium',
                'cloud_20g',
                'cloud_40g',
                'points_purchase',
                'task',
                'saas_tool_paid',
                'saas_tool_trail',
                'service_substitute',
                'service_correct',
            ],
        },
        categoryType: { type: String },
        mode: { type: String, required: true, default: 'percentage', enum: ['fixed', 'percentage'] },
        value: { type: Number, required: true }, //固定数值;按比例
    }, {
        timestamps: true,
    });
    schema.index({ tab: 1, category: 1, categoryType: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
