"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const auth_class_1 = require("./auth.class");
const auth_hooks_1 = __importDefault(require("./auth.hooks"));
function default_1(app) {
    const options = {
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/auth', new auth_class_1.Auth(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('auth');
    service.hooks(auth_hooks_1.default);
}
exports.default = default_1;
