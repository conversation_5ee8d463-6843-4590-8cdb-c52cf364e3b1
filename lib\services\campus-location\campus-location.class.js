"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CampusLocation = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const apiKey = 'AIzaSyDh-aLBR7Gzd8-XQOKQAdoExwlIoi_AHEk';
const axios_1 = __importDefault(require("axios"));
class CampusLocation extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async getCountry({}, params) {
        let list = await this.Model.distinct('country');
        return { list };
    }
    async getCityByCountry({ country }, params) {
        let list = await this.Model.distinct('city', { country });
        return { list };
    }
    async getCity({}, params) {
        let list = await this.Model.find().select(['country', 'city']);
        return { list };
    }
    async handleStatistics({ uids, subjectIds, mentoringType, subjectData }) {
        let auth = await this.app.service('service-auth').Model.find({ uid: { $in: uids }, status: 2, subject: { $in: subjectIds } });
        let topicDict = {};
        let subjectDict = {};
        if (mentoringType == 'steam') {
            subjectData[0].topic.forEach((e) => {
                topicDict[e._id] = {
                    name: e.name,
                    subject: subjectData[0]._id,
                };
            });
        }
        else {
            subjectData.forEach((e) => {
                subjectDict[e._id] = {
                    name: e.name,
                    subject: e._id,
                };
            });
        }
        let statistics = {};
        for (let j = 0; j < auth.length; j++) {
            const e = auth[j];
            if (mentoringType == 'steam') {
                for (let m = 0; m < e.topic.length; m++) {
                    const topic = e.topic[m];
                    if (statistics[topic._id]) {
                        statistics[topic._id].count += 1;
                    }
                    else {
                        if (!topicDict[topic._id]) {
                            continue;
                        }
                        statistics[topic._id] = {
                            name: topicDict[topic._id].name,
                            count: 1,
                            grades: {},
                            subject: topicDict[topic._id].subject,
                        };
                    }
                    // 按grades统计
                    for (let k = 0; k < e.grades.length; k++) {
                        const grade = e.grades[k];
                        if (statistics[topic._id].grades[grade]) {
                            statistics[topic._id].grades[grade].count += 1;
                            statistics[topic._id].grades[grade].list.push(e);
                        }
                        else {
                            statistics[topic._id].grades[grade] = {
                                name: grade,
                                count: 1,
                                list: [e],
                            };
                        }
                    }
                }
            }
            else {
                if (statistics[e.subject]) {
                    statistics[e.subject].count += 1;
                }
                else {
                    if (!subjectDict[e.subject]) {
                        continue;
                    }
                    statistics[e.subject] = {
                        name: subjectDict[e.subject].name,
                        count: 1,
                        grades: {},
                        subject: subjectDict[e.subject].subject,
                    };
                }
                // 按grades统计
                for (let k = 0; k < e.grades.length; k++) {
                    const grade = e.grades[k];
                    if (statistics[e.subject].grades[grade]) {
                        statistics[e.subject].grades[grade].count += 1;
                        statistics[e.subject].grades[grade].list.push(e);
                    }
                    else {
                        statistics[e.subject].grades[grade] = {
                            name: grade,
                            count: 1,
                            list: [e],
                        };
                    }
                }
            }
        }
        let statisticsArr = [];
        for (let key in statistics) {
            let item = statistics[key];
            if (mentoringType == 'steam') {
                item.topic = key;
            }
            statisticsArr.push(item);
        }
        return statisticsArr;
    }
    // 统计 按认证项 grade分别统计
    async getCityTeacherStatistics({ country, mentoringType, subject }, params) {
        let list = [];
        let cities = await this.Model.distinct('city', { country });
        let subjectData;
        if (mentoringType == 'steam') {
            subjectData = await this.app.service('subjects').Model.find({ name: 'STEAM', uid: 1 });
        }
        else {
            subjectData = await this.app.service('subjects').Model.find({ _id: { $in: subject }, uid: 1 });
        }
        let subjectIds = subjectData.map((e) => e._id);
        for (let i = 0; i < cities.length; i++) {
            const city = cities[i];
            let teacher = await this.app.service('service-conf').Model.find({ city, status: 2 });
            let uids = teacher.map((e) => e._id);
            let statisticsArr = await this.handleStatistics({ uids, subjectIds, mentoringType, subjectData });
            list.push({
                country,
                city,
                statistics: statisticsArr,
            });
        }
        return list;
    }
    async getCityTeacherStatisticsRadius({ country, city, place_id, mentoringType, subject }, params) {
        let { lng, lat } = await this.getLocationByPlaceId({ place_id });
        let teacher = await this.app.service('service-conf').Model.aggregate([
            {
                $geoNear: {
                    near: {
                        type: 'Point',
                        coordinates: [lng, lat],
                    },
                    distanceField: 'distance',
                    spherical: true,
                },
            },
            { $match: { country, city } },
        ]);
        teacher = teacher.filter((e) => e.distance <= e.serviceRadius);
        let subjectData;
        if (mentoringType == 'steam') {
            subjectData = await this.app.service('subjects').Model.find({ name: 'STEAM', uid: 1 });
        }
        else {
            subjectData = await this.app.service('subjects').Model.find({ _id: { $in: subject }, uid: 1 });
        }
        let subjectIds = subjectData.map((e) => e._id);
        let uids = teacher.map((e) => e._id);
        let statisticsArr = await this.handleStatistics({ uids, subjectIds, mentoringType, subjectData });
        return statisticsArr;
    }
    // 按半径过滤推送老师
    async getPushTeacherByRadius({ country, city, place_id, uids }, params) {
        let { lng, lat } = await this.getLocationByPlaceId({ place_id });
        let teacher = await this.app.service('service-conf').Model.aggregate([
            {
                $geoNear: {
                    near: {
                        type: 'Point',
                        coordinates: [lng, lat],
                    },
                    distanceField: 'distance',
                    spherical: true,
                },
            },
            { $match: { country, city, _id: { $in: uids } } },
        ]);
        teacher = teacher.filter((e) => e.distance <= e.serviceRadius);
        return teacher.map((e) => e._id.toString());
    }
    async getLocationByPlaceId({ place_id }) {
        // var tunnel = require('tunnel')
        let url = `https://maps.googleapis.com/maps/api/geocode/json?&key=${apiKey}&place_id=${place_id}`;
        let rs = await (0, axios_1.default)({
            method: 'post',
            url: url,
            // proxy: false,
            // httpAgent: tunnel.httpOverHttp({proxy: {host: '127.0.0.1', port: '7890'}}),
            // httpsAgent: tunnel.httpsOverHttp({proxy: {host: '127.0.0.1', port: '7890'}}),
        });
        let { lng, lat } = rs.data.results[0].geometry.location;
        return { lng, lat };
    }
    async updateLocation({ _id, place_id, model }) {
        let { lat, lng } = await this.getLocationByPlaceId({ place_id });
        await this.app.service(model).Model.updateOne({ _id: _id }, {
            $set: {
                location: {
                    type: 'Point',
                    coordinates: [lng, lat],
                },
            },
        });
    }
}
exports.CampusLocation = CampusLocation;
