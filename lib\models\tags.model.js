"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'tags';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const tag = {
        name: { type: String, trim: true },
        prompt: { type: String, trim: true },
    };
    const schema = new Schema({
        uid: { type: String, trim: true, required: true },
        sort: { type: Number, default: -1 },
        subtitle: { type: String, trim: true },
        curriculum: { type: [String] },
        source: {
            // import sys data
            _id: { type: String },
            readonly: { type: Boolean, default: false },
            curriculum: { type: [String] },
            set: { type: [String] }, // related curriculum name
        },
        count: { type: [Number] },
        step: { type: String, enum: Agl.tagsStep },
        stepTask: { type: String, enum: Agl.tagsStep },
        prompt: { type: String, trim: true },
        layer: { type: Boolean, default: false },
        tool: { type: Boolean, default: false },
        set: { type: String, trim: true, required: true },
        child: [{ ...tag, child: [tag] }],
        configured: { type: Boolean, default: false },
        publish: { type: Boolean, default: false },
        snapshot: { type: Schema.Types.Mixed },
    }, {
        timestamps: true,
    });
    // schema.index({ uid: 1, set: -1, set1: -1 }, { sparse: true, unique: true })
    // schema.index({ uid: 1, tags: -1 }, { sparse: true, unique: true })
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
