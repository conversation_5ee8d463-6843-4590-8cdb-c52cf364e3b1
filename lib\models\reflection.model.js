"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'reflection';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        mode: { type: String, enum: Agl.reflectionMode, default: 'refl' },
        uid: { type: String, required: true },
        pid: { type: String, trim: true },
        to: { type: [String], sparse: true },
        public: { type: Boolean, default: true },
        // private comment, public: {to: null}, public+private: {to: user._id}
        unit: { type: String, required: true },
        rkey: { type: String, required: true },
        session: { type: String, trim: true },
        school: { type: String, sparse: true, trim: true },
        classId: { type: String, trim: true },
        content: { type: String, required: true, trim: true },
        attach: { type: [String] },
        visible: { type: String, enum: Agl.reflectionVisible }, // 弃用
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
