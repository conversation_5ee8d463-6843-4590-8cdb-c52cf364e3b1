"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'drawpm';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        _id: { type: String, required: true },
        uid: { type: String },
        mark: { type: String, trim: true },
        type: { type: String, required: true },
        version: { type: Number, default: 2 },
        source: { type: String },
        appState: { type: Schema.Types.Mixed, default: {} },
        elements: { type: Schema.Types.Mixed, default: [] },
        files: { type: Schema.Types.Mixed, default: {} },
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
