"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackUserData';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        packUser: { type: String, required: true },
        expired: { type: Date },
        status: { type: Number, default: 0 },
        order: { type: String, trim: true },
        serviceTicket: { type: String, trim: true },
        payMethod: { type: String, trim: true, enum: ['cash', 'point', 'gift'] }, // 支付方式
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
