"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackUser';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        total: { type: Number, default: 0 },
        used: { type: Number, default: 0 },
        expireSoon: { type: Date },
        session: {
            _id: { type: String },
            name: { type: String },
            start: { type: String },
            end: { type: String }, // session.end
        },
        snapshot: { type: Schema.Types.Mixed, required: true },
        status: { type: Boolean, default: true },
        order: { type: String, required: true },
        price: { type: Number, default: 0 },
        point: { type: Number },
        isPoint: { type: Boolean, default: false },
        payMethod: { type: String },
        // 主题服务包用 https://github.com/zran-nz/bug/issues/5196
        pid: { type: String },
        premium: { type: String },
        taskIndex: { type: [String] },
        tasks: { type: [String] },
        // 线下包
        country: { type: String },
        city: { type: String },
        address: { type: String, trim: true },
        place_id: { type: String, trim: true },
        location: {
            type: {
                type: String,
                enum: ['Point'],
            },
            coordinates: {
                type: [Number],
            },
        },
        servicePremium: { type: String },
        participants: { type: [String] },
        zoom: {
            enabled: { type: Boolean },
            maxParticipants: { type: Boolean },
            max: { type: Number },
            min: { type: Number },
        },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
