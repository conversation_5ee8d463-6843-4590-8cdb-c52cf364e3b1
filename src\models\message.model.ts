// message-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'message'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, required: true},
      rid: {type: String, required: true}, // 关联的 service-auth._id, service-conf._id, school-plan._id, section._id
      message: {type: String},
      read: {type: Boolean, default: false}, // read status
      role: {type: String, default: 'user', enum: ['admin', 'user']}, // 发送用户
      /**
       * type rid对应关系
       * service-auth-premium service-auth._id
       * session-takeaway session._id
       */
      type: {
        type: String,
        default: 'service-auth',
        enum: [
          'service-auth',

          'service-pack-apply',

          'school-plan',

          'service-auth-premium',

          'classes',

          'session-takeaway',

          'prompts',

          'unit',

          'service-conf',
          'ambassador-auth',
          ,
          'section',
        ],
      },
      attachments: {
        // 图片/视频证据
        filename: {type: String, trim: true}, // 文件名
        mime: {type: String, trim: true}, // 文件 MIME
        hash: {type: String, trim: true}, // 文件SHA1, files._id
      },
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
