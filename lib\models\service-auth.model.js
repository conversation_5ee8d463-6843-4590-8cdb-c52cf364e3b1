"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'serviceAuth';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, index: true, required: true },
        type: { type: String, index: true, required: true, enum: Agl.ServiceType },
        mentoringType: { type: String, index: true, enum: Agl.MentoringType },
        enable: { type: Boolean, index: true, required: true },
        serviceRoles: { type: [String], index: true, enum: Agl.ServiceRoles, default: Agl.ServiceRoles },
        serviceRolesUser: { type: [String], enum: Agl.ServiceRoles, default: Agl.ServiceRoles },
        countryCode: { type: [String], index: true, trim: true },
        curriculum: { type: String, index: true, trim: true },
        subject: { type: String, index: true, trim: true },
        gradeGroup: { type: [String], index: true, trim: true },
        grades: { type: [String], trim: true },
        tags: { type: [String], index: true, trim: true },
        unit: {
            // 认证课件
            _id: { type: String, trim: true },
            name: { type: String, trim: true },
            price: { type: Number }, // 课件价格, 单位 分 = 互动题数量*20
        },
        ability: { type: String, trim: true },
        styles: { type: [String], trim: true },
        otherStyles: { type: [String], trim: true },
        unitSnapshot: { type: Schema.Types.Mixed },
        linkSnapshot: { type: Schema.Types.Mixed },
        topic: [
            // 用于 essay, teacherTraining等大纲层级 认证项
            {
                _id: { type: String, trim: true },
                label: { type: [String], trim: true }, // subjects.topic...name
            },
        ],
        desc: { type: String, trim: true },
        status: { type: Number, index: true, default: 0 },
        approval: {
            // 审批信息
            submitted: { type: Date },
            approved: { type: Date },
            approver: { type: String }, // 审核人uid
        },
        attachments: [
            // 附件
            {
                filename: { type: String, trim: true },
                mime: { type: String, trim: true },
                hash: { type: String, trim: true },
                date: { type: Date },
                type: { type: String, trim: true },
                size: { type: Number }, // 文件大小
            },
        ],
        versionId: { type: String, trim: true },
        reason: { type: String, trim: true },
        inviter: { type: String, trim: true },
        qualification: { type: String, trim: true },
        feedback: {
            // 留言反馈
            message: { type: String },
            date: { type: Date },
            read: { type: Boolean, default: false },
            reply: { type: String },
            replyDate: { type: Date },
            replyRead: { type: Boolean, default: false }, // read status
        },
        follower: { type: String },
        followedAt: { type: Date, index: true },
        releasedAt: { type: Date },
        schoolOfFollower: { type: String },
        importUsers: { type: [String] },
        interviewInvited: { type: Boolean, default: false },
        interviewPack: { type: String },
        interviewApply: { type: Boolean, default: false },
        takeaway: { type: String },
        takeawayCreatedAt: { type: Date },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
