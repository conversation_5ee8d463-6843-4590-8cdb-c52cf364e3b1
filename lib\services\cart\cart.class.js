"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cart = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class Cart extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async goodsFilter(query, user) {
        let list = await this.Model.find(query);
        let linkIds = list.map((item) => item.goodsId);
        let order = await this.app.service('order').Model.find({
            buyer: user._id,
            status: { $in: [100, 200, 600] },
            links: { $elemMatch: { id: { $in: linkIds }, removed: { $exists: false } } },
        });
        let orderedLinks = [];
        for (let i = 0; i < order.length; i++) {
            const item = order[i];
            for (let j = 0; j < item.links.length; j++) {
                const link = item.links[j];
                if (!link.removed) {
                    orderedLinks.push(link.id);
                }
            }
        }
        for (let i = 0; i < list.length; i++) {
            const item = list[i];
            let goods;
            try {
                if (item.style == 'unit') {
                    goods = await this.app.service('unit').Model.findOne({ _id: item.goodsId }).select(this.app.service('unit').selectList);
                }
                else if (item.style == 'session') {
                    goods = await this.app.service('session').Model.findOne({ _id: item.goodsId }).select(this.app.service('session').selectList);
                }
            }
            catch (e) {
                goods = false;
            }
            if (!goods || (item.style === 'unit' && !goods.publish.lib) || orderedLinks.includes(item.goodsId)) {
                await this.app.service('cart').remove(item._id);
                list.splice(i, 1);
                i--;
                continue;
            }
        }
    }
    // 数量统计
    async getCount({}, params) {
        var _a, _b;
        await this.goodsFilter({ buyer: (_a = params === null || params === void 0 ? void 0 : params.user) === null || _a === void 0 ? void 0 : _a._id }, params.user);
        let count = await this.Model.count({ buyer: (_b = params.user) === null || _b === void 0 ? void 0 : _b._id });
        return {
            count,
        };
    }
}
exports.Cart = Cart;
