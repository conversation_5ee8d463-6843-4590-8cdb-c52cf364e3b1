"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'sessionTakeawaySnapshot';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        session: { type: String, required: true },
        school: { type: String },
        classId: { type: String },
        uid: { type: String, required: true },
        response: { type: [Schema.Types.Mixed] },
        comments: { type: [Schema.Types.Mixed] },
        hash: { type: String, trim: true }, // hash 发送报告的时候会生成，代表发送过
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
