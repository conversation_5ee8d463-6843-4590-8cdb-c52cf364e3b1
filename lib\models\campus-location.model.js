"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'campusLocation';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        country: { type: String, trim: true },
        city: { type: String, trim: true },
        experienceRate: { type: Object },
        tutorRate: { type: Object },
        compensationHour: { type: Number },
        attachmentsCity: [
            {
                filename: { type: String, trim: true },
                mime: { type: String, trim: true },
                hash: { type: String, trim: true }, // 文件SHA1, files._id
            },
        ],
        archive: { type: Boolean, default: false },
    }, {
        timestamps: true,
    });
    schema.index({ country: 1, city: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
