"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'unitTpl';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true },
        curriculum: { type: String, required: true },
        curricId: { type: String, required: true },
        group: { type: String, required: true },
        name: { type: String, required: true },
        mode: { type: String, required: true, default: 'unit', enum: Agl.unitMode },
        data: [
            {
                code: { type: String, sparse: true },
                // required: { type: Boolean, sparse: true }, // private variable
                // origin: { type: String, sparse: true }, // private variable
                enable: { type: Boolean, default: true },
                type: { type: String, required: true },
                name: { type: String, required: true },
                group: { type: String, required: true },
                prompt: { type: String, sparse: true },
                tags: { type: String, sparse: true },
                diy: { type: Boolean, default: false }, // tag diy
            },
        ], //
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
