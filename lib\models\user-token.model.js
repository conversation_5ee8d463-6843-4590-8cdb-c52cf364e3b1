"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'userToken';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sub: { type: String, required: true, unique: true },
        type: { type: String, default: 'google' },
        avatar: { type: String, trim: true },
        locale: { type: String, trim: true },
        name: { type: String, trim: true },
        fname: { type: [String], trim: true },
        scope: { type: String, trim: true },
        token: { type: String, required: true },
        exp: { type: Number },
        email: { type: String, trim: true },
        ext: { type: Schema.Types.Mixed },
        rt: { type: String, required: true },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
