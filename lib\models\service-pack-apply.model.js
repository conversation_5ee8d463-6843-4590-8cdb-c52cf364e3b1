"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackApply';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        servicePack: { type: String, required: true },
        sharedSchool: { type: String },
        name: [{ type: String, trim: true }],
        nickname: { type: String, sparse: true, trim: true },
        gender: { type: String, trim: true },
        mobile: { type: String, trim: true },
        email: { type: String, lowercase: true, trim: true },
        emailType: { type: String, enum: ['student', 'parent'] },
        mentoringType: { type: String, enum: Agl.MentoringType },
        serviceTicket: { type: [String], default: [] },
        order: { type: [String] },
        withinSchool: { type: Boolean, default: false },
        needOrder: { type: Boolean, default: false },
        attachments: [
            // 附件Classcipe1
            {
                filename: { type: String, trim: true },
                mime: { type: String, trim: true },
                hash: { type: String, trim: true },
                date: { type: Date },
                type: { type: String, trim: true },
                size: { type: Number }, // 文件大小
            },
        ],
        academicStatus: { type: Number, default: 0 },
        needAcademic: { type: Boolean, default: false },
        interviewStatus: { type: Number, default: 0 },
        needInterview: { type: Boolean, default: false },
        interviewInvited: { type: Boolean, default: false },
        reason: { type: String, trim: true },
        status: { type: Number, default: 0 },
        interviewPack: { type: String },
        interviewOrder: { type: String },
        interviewApply: { type: Boolean, default: false },
        takeaway: { type: String },
        takeawayId: { type: String },
        takeawayCreatedAt: { type: Date },
        archive: { type: Boolean, default: false },
        contentOrientated: [
            {
                premium: { type: String },
                times: { type: Number },
                price: { type: Number, trim: true },
                schoolPrice: { type: Number, trim: true },
                servicePack: { type: String }, // 捆绑的服务包, service-pack._id 服务包id
            },
        ],
        purchaseExpireAt: { type: Date },
        interviewPurchaseExpireAt: { type: Date },
        interviewPurchaseExpired: { type: Boolean, default: false },
        follower: { type: String },
        followedAt: { type: Date },
        schoolOfFollower: { type: String },
        approvedAt: { type: Date },
        feedback: {
            // 留言反馈
            message: { type: String },
            date: { type: Date },
            read: { type: Boolean, default: false },
            reply: { type: String },
            replyDate: { type: Date },
            replyRead: { type: Boolean, default: false }, // read status
        },
    }, {
        timestamps: true,
    });
    schema.index({ uid: 1, servicePack: 1, sharedSchool: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
