"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'collab';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        rid: { type: String, required: true },
        type: { type: String, required: true, enum: Agl.unitMode },
        uid: { type: String, required: true },
        guest: { type: String, sparse: true, enum: [null, ...Agl.collabRole] },
        members: [
            {
                email: { type: String, required: true, trim: true },
                nickname: { type: String },
                avatar: { type: String },
                role: { type: String, enum: Agl.collabRole },
                permissionId: { type: String },
                status: { type: Boolean, default: false },
                message: { type: String, trim: true },
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
