"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'skills';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const outline6 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
    });
    const outline5 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline6],
    });
    const outline4 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline5],
    });
    const outline3 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline4],
    });
    const outline2 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline3],
    });
    const outline1 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline2],
    });
    const outline = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline1],
    });
    const schema = new Schema({
        uid: { type: String, index: true, required: true },
        name: { type: String, required: true, trim: true },
        subtitle: { type: String, trim: true },
        curriculum: { type: [String], required: true },
        participants: { type: String, sparse: true, enum: Agl.subjectsParticipants },
        grade: { type: [String], trim: true },
        del: { type: Boolean, index: true, default: false },
        count: {
            standard: { type: [Number] },
        },
        standardLevel: { type: [String], trim: true },
        standard: [outline],
        code: {
            standard: { type: String, trim: true }, // curriculmCode:subjectCode
        },
        source: {
            // import sys data
            standardCurriculum: { type: [String] },
            standardSet: { type: [String] }, // curriculum.title + subject.title
        },
        publish: { type: [String] },
        snapshot: { type: Schema.Types.Mixed },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
