// Initializes the `income-setting` service on path `/income-setting`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {IncomeSetting} from './income-setting.class'
import createModel from '../../models/income-setting.model'
import hooks from './income-setting.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'income-setting': IncomeSetting & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/income-setting', new IncomeSetting(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('income-setting')

  service.hooks(hooks)
}
