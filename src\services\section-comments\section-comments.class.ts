// src/services/journal-comments/journal-comments.class.ts

import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Id, Params} from '@feathersjs/feathers'
import {BadRequest, Forbidden, NotFound} from '@feathersjs/errors'

export class SectionComments extends Service {
  app: Application
  options: any
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.options = options
    this.app = app
  }
  async create(data: any, params: Params) {
    const user = params.user ?? {}
    const {sectionId, cover, mime} = data

    if (!sectionId || !cover) {
      throw new BadRequest('sectionId and cover are required')
    }

    const fileEntry = {
      userId: user._id,
      name: user.name || [],
      nickname: user.nickname || '',
      avatar: user.avatar || '',
      cover,
      mime,
      uploadedTime: new Date(),
    }

    const fileData = {
      sectionId,
      files: fileEntry,
    }
    return super.create(fileData, params)
  }

  async addComment(id: string, data: {comment: string}, params: Params) {
    const userId = params.user?._id || params.user?.id
    if (!userId) throw new Error('User not authenticated')

    const newComment = {
      userId,
      comment: data.comment,
      createdAt: new Date(),
    }

    return await this.Model.findByIdAndUpdate(
      id,
      {
        $push: {
          comments: {
            $each: [newComment],
            $position: 0,
          },
        },
      },
      {new: true}
    )
  }
}
