"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const agreement_class_1 = require("./agreement.class");
const agreement_model_1 = __importDefault(require("../../models/agreement.model"));
const agreement_hooks_1 = __importDefault(require("./agreement.hooks"));
function default_1(app) {
    const options = {
        Model: (0, agreement_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/agreement', new agreement_class_1.Agreement(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('agreement');
    service.hooks(agreement_hooks_1.default);
}
exports.default = default_1;
