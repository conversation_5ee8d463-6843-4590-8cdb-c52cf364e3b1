"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
const http = require('http');
const https = require('https');
function default_1(app) {
    const proxyOld = (req, res) => {
        const { method, headers, body, Aip, url } = req;
        let purl;
        let client;
        let type;
        if (!url.includes('/proxyOld2/')) {
            client = https;
            type = 'proxyOld';
            purl = url.replace('/proxyOld/', 'https://dev.classcipe.com/classcipe/');
        }
        else {
            client = http;
            type = 'proxyOld2';
            purl = url.replace('/proxyOld2/', 'http://***********:8088/classcipe/');
            // purl = url.replace('/proxyOld2/', 'http://127.0.0.1:8088/classcipe/')
        }
        const ubj = new URL(purl);
        const _st = Date.now();
        const creq = client.request(purl, { method, headers }, (cres) => {
            res.writeHead(cres.statusCode, cres.headers);
            logger_1.default.log(type, method, url);
            cres.pipe(res).on('close', () => {
                const ttl = Date.now() - _st;
                logger_1.default.info('Close:', Aip, type, method, url, ttl);
                creq.destroy();
                app.service('log').Model.create({ ip: Aip, ttl, type, date: new Date(), method, body, href: url, origin: ubj.origin, pathname: ubj.pathname, ua: headers['user-agent'], cookie: headers.cookie }).then();
            });
        });
        req.pipe(creq).on('close', () => {
            console.log('req close', url);
            creq.destroy();
            req.destroy();
        });
    };
    app.all(/proxyOld\/.*/, proxyOld);
    app.all(/proxyOld2\/.*/, proxyOld);
}
exports.default = default_1;
