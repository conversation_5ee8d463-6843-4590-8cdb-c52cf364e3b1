import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class TeachingAccident extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // 未读统计
  async getUnreadCount({}: any, params: Params): Promise<any> {
    let list: any = await this.Model.find({read: false})
    let dict: any = {}
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      for (let j = 0; j < item.tags.length; j++) {
        const tag = item.tags[j]
        if (dict[tag]) {
          dict[tag]++
        } else {
          dict[tag] = 1
        }
      }
    }
    return {
      unread: list.length,
      detail: dict,
    }
  }

  async handleApprove({_id, data}: any, params: Params): Promise<any> {
    let res: any = await this.Model.findOne({_id})
    let {status, days = 0} = data
    let {endAt} = await this.app.service('suspend-class').suspend({type: 'teaching-accident', accidentId: _id, status, days, uid: res.teacher})
    res.endAt = endAt
    this.send(res, 'TeachingAccidentApproved', params, days == 0 ? true : false)
    if (!res.serviceReturn) {
      let oldSession = await this.app.service('session').Model.findOne({_id: res.session})
      let bookingData: any = await this.app.service('service-booking').Model.findOne({_id: res.serviceBooking})
      let serviceData = await this.app.service('service-pack-user-data').Model.find({_id: {$in: bookingData.packUserData}})
      for (let i = 0; i < serviceData.length; i++) {
        const item: any = serviceData[i]
        this.app.service('service-pack-user-data').add(
          {
            packUser: res.servicePackUser,
            type: 'teachingAccident',
            times: 1,
            payMethod: 'gift',
            order: item.order,
            serviceTicket: item.serviceTicket,
            servicer: res.teacher,
            oldSession,
          },
          params
        )
      }
      await this.Model.updateOne({_id}, {serviceReturn: true})
    }
  }

  async extData(one: any, params?: Params) {
    one.studentInfo = await this.app.service('users').uidToInfo(one.student)
    one.teacherInfo = await this.app.service('users').uidToInfo(one.teacher)
    const suspend = await this.app.service('suspend-class').Model.findOne({accident: one._id})
    one.suspend = suspend
  }

  async send(doc: any, tpl: string, params: Params, withoutSuspension = false) {
    const {session, sessionName, student, teacher, checkReason, serviceType, serviceName, tags, evidencesStudent, endAt, servicePackUser} = doc
    const studentInfo: any = await this.app.service('users').uidToInfo(student)
    const teacherInfo: any = await this.app.service('users').uidToInfo(teacher)
    const packUserData: any = await this.app.service('service-pack-user').Model.findById(servicePackUser)

    let url = ''
    let url2 = ''
    if (tpl === 'TeachingAccidentReported') {
      if (packUserData?.associatedTask) {
        url = `${SiteUrl}/v2/study/purchased?tab=myAssociatedTask&subtab=ongoing` //need to fix this the correct url, where to fix write the url for teacher side
      }
      url = `${SiteUrl}/v2/detail/session/${session}?evaluation=true`
    } else if (tpl === 'TeachingAccidentApproved') {
      url = `${SiteUrl}/help/#/main/terms`
      url2 = `${SiteUrl}/v2/detail/session/${session}`
    } else if (tpl === 'TeachingAccidentRejected') {
      url = `${SiteUrl}/help/#/main/terms`
      url2 = `${SiteUrl}/v2/detail/session/${session}`
    }

    let needSuffix = false
    if (tpl === 'TeachingAccidentReported') {
      needSuffix = true
    }
    // 发给学生
    await this.app.service('notice-tpl').mailto(
      tpl + `${needSuffix ? 'Student' : ''}`,
      studentInfo.email,
      {
        username: studentInfo.name.join(' '),
        session_name: packUserData?.associatedTask ? packUserData.snapshot.name : sessionName,
        check_reason: checkReason,
        url,
        service_type: serviceType,
        service_name: serviceName,
        tags: tags.join(','),
        reason: evidencesStudent[0].content,
        start: new Date().toLocaleDateString(),
        end: new Date(endAt).toLocaleDateString(),
        student_name: studentInfo.name.join(' '),
        teacher_name: teacherInfo.name.join(' '),
        url2,
      },
      params.user?._id
    )
    // 发给老师
    await this.app.service('notice-tpl').mailto(
      tpl + `${needSuffix ? 'Teacher' : ''}`,
      teacherInfo.email,
      {
        username: teacherInfo.name.join(' '),
        session_name: packUserData?.associatedTask ? packUserData.snapshot.name : sessionName,
        check_reason: checkReason,
        url,
        service_type: serviceType,
        service_name: serviceName,
        tags: tags.join(','),
        reason: evidencesStudent[0].content,
        start: new Date().toLocaleDateString(),
        end: new Date(endAt).toLocaleDateString(),
        student_name: studentInfo.name.join(' '),
        teacher_name: teacherInfo.name.join(' '),
        url2,
      },
      params.user?._id
    )

    // 有停课,发送额外通知给老师
    if (!withoutSuspension && tpl === 'TeachingAccidentApproved') {
      await this.app.service('notice-tpl').mailto(
        'TeachingAccidentApprovedWithSuspensionTeacher',
        teacherInfo.email,
        {
          username: teacherInfo.name.join(' '),
          session_name: sessionName,
          check_reason: checkReason,
          url,
          service_type: serviceType,
          service_name: serviceName,
          tags: tags.join(','),
          reason: evidencesStudent[0].content,
          start: new Date().toLocaleDateString(),
          end: new Date(endAt).toLocaleDateString(),
          student_name: studentInfo.name.join(' '),
          teacher_name: teacherInfo.name.join(' '),
          url2,
        },
        params.user?._id
      )
    }
  }
}
