// Initializes the `bonus-setting` service on path `/bonus-setting`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { BonusSetting } from './bonus-setting.class';
import createModel from '../../models/bonus-setting.model';
import hooks from './bonus-setting.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'bonus-setting': BonusSetting & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/bonus-setting', new BonusSetting(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('bonus-setting');

  service.hooks(hooks);
}
