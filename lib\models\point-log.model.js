"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'pointLog';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        tab: { type: String, enum: ['earn', 'claim'] },
        source: { type: String, required: true },
        category: { type: String, required: true },
        categoryType: { type: String },
        value: { type: Number, required: true },
        total: { type: Number, required: true },
        businessId: { type: String },
        snapshot: { type: Object },
        status: { type: Number, enum: [0, 1], default: 1 },
        isSchool: { type: Boolean, default: false }, //是否是学校
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
