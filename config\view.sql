SELECT
  `ta_unit_plan`.`id` AS `id`,
  `ta_unit_plan`.`name` AS `name`,
  `ta_unit_plan`.`image` AS `image`,
  `ta_unit_plan`.`status` AS `status`,
  `ta_unit_plan`.`del_flag` AS `del_flag`,
  `ta_unit_plan`.`create_by` AS `create`,
  `ta_unit_plan`.`create_time` AS `createdAt`,
  `ta_unit_plan`.`update_time` AS `updatedAt`,
  `ta_unit_plan`.`source_from` AS `source`,
  `ta_unit_plan`.`price` AS `price`,
  2 AS `type`,
  NULL AS `sid`,
  NULL AS `rev`,
  `ta_unit_plan`.`overview` AS `description`,
  NULL AS `pid`,
  NULL AS `isEdit`,
  `ta_unit_plan`.`can_publish` AS `isOk` 
FROM
	`ta_unit_plan` UNION ALL
SELECT
	`cc_task`.`id` AS `id`,
	`cc_task`.`name` AS `name`,
	`cc_task`.`image` AS `image`,
	`cc_task`.`status` AS `status`,
	`cc_task`.`del_flag` AS `del_flag`,
	`cc_task`.`create_by` AS `create`,
	`cc_task`.`create_time` AS `createdAt`,
	`cc_task`.`update_time` AS `updatedAt`,
	`cc_task`.`source_from` AS `source`,
	`cc_task`.`price` AS `price`,
	4 AS `type`,
	`cc_task`.`presentation_id` AS `sid`,
	`cc_task`.`revision_id` AS `rev`,
	`cc_task`.`overview` AS `description`,
	`cc_task`.`parent_task_id` AS `pid`,
	`cc_task`.`slide_editing` AS `isEdit`,
	`cc_task`.`can_publish` AS `isOk`
FROM
	`cc_task` UNION ALL
SELECT
	`cc_content`.`id` AS `id`,
	`cc_content`.`name` AS `name`,
	`cc_content`.`image` AS `image`,
	`cc_content`.`status` AS `status`,
	`cc_content`.`del_flag` AS `del_flag`,
	`cc_content`.`create_by` AS `create`,
	`cc_content`.`create_time` AS `createdAt`,
	`cc_content`.`update_time` AS `updatedAt`,
	`cc_content`.`source_from` AS `source`,
	`cc_content`.`price` AS `price`,
	`cc_content`.`type` AS `type`,
	`cc_content`.`presentation_id` AS `sid`,
	`cc_content`.`revision_id` AS `rev`,
	`cc_content`.`goals` AS `description`,
	NULL AS `pid`,
	`cc_content`.`slide_editing` AS `isEdit`,
	`cc_content`.`can_publish` AS `isOk`
FROM
	`cc_content`