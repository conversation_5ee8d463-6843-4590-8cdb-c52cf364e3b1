"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'users';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        name: [{ type: String, sparse: true, trim: true }],
        nickname: { type: String, sparse: true, trim: true },
        password: { type: String, sparse: true },
        avatar: { type: String, sparse: true },
        email: { type: String, lowercase: true, trim: true, sparse: true, unique: true },
        mobile: { type: String, trim: true, sparse: true, unique: true },
        countryCode: { type: String, lowercase: true, trim: true, sparse: true },
        roles: { type: [String], sparse: true, enum: Agl.usersRoles },
        managerRoles: { type: [String], sparse: true, enum: Agl.usersManagerRoles },
        gender: { type: String, sparse: true, trim: true },
        intro: { type: String, sparse: true, trim: true },
        google: { type: String, sparse: true, unique: true },
        zoom: { type: String, sparse: true, unique: true },
        lang: { type: String, trim: true, default: 'en-US' },
        timeZone: { type: String, trim: true },
        tz: { type: Number, trim: true },
        ip: { type: String },
        last: { type: Date },
        emergencyContact: { type: String, trim: true, sparse: true },
        // for teacher
        teacherExt: {
            curriculum: { type: [String], trim: true },
            subjects: { type: [String], trim: true },
            grades: { type: Schema.Types.Mixed, trim: true },
        },
        // for student
        studentId: { type: String, lowercase: true, trim: true, sparse: true, unique: true },
        studentExt: {
            dob: { type: String, trim: true },
            parent: {
                mobile: { type: String, trim: true },
                email: { type: String, trim: true },
            },
            curriculum: { type: String, trim: true },
            subjects: { type: [String], trim: true },
            grades: { type: [String], trim: true },
        },
        // for service
        freeServiceType: { type: Schema.Types.Mixed },
        inviteCode: { type: String, trim: true },
        inviter: { type: String, trim: true },
        point: { type: Number, default: 0 },
        balance: { type: Number, default: 0 },
        freeServiceClaimed: { type: Boolean, default: false },
        freePromptCount: { type: Number, default: 0 },
        agreedToTerms: { type: Boolean, default: false },
        serviceCalendarEmail: { type: Boolean, default: false }, // service-auth审批通过时且认证项包含4个中的一个,每个用户通知一次,#6056 通知155
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
