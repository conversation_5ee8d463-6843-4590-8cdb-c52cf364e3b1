"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'slides';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String },
        // task: {type: String, sparse: true, trim: true}, // view_content.id, 已经弃用
        id: { type: String, required: true, trim: true },
        rev: { type: String, sparse: true, trim: true },
        pages: [
            {
                _id: { type: String, trim: true },
                url: { type: String, trim: true },
                outline: { type: [String] },
                assess: { type: [String] },
                pic: { type: String },
                // hash: { type: String }, // SHA1(JSON.stringify(slides.pageElements))
                size: { type: Number }, // pic size
            },
        ],
        hash: { type: String }, // pptx file s3 key
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
