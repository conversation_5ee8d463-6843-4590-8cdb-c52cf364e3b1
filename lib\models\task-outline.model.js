"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'taskOutline';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        task: { type: String, unique: true, required: true },
        outline: {
            curr: { type: String, trim: true },
            grades: { type: [Number], trim: true },
            subjects: { type: [String], trim: true },
            selects: { type: [String], trim: true },
            tags: { type: Schema.Types.Mixed },
            bloom: { type: Schema.Types.Mixed },
            data: { type: Schema.Types.Mixed } // selects format list { code: { code, name, child: [{ _id, name, child: [] }] }}
        },
        assess: {
            curr: { type: String, trim: true },
            subjects: { type: [String], trim: true },
            selects: { type: [String], trim: true },
            bloom: { type: Schema.Types.Mixed },
            data: { type: Schema.Types.Mixed }
        },
        pd: {
            curr: { type: String, trim: true },
            subjects: { type: [String], trim: true },
            selects: { type: [String], trim: true },
            tags: { type: Schema.Types.Mixed },
            bloom: { type: Schema.Types.Mixed },
            data: { type: Schema.Types.Mixed }
        },
        skills: {
            curr: { type: String, trim: true },
            subjects: { type: [String], trim: true },
            selects: { type: [String], trim: true },
            tags: { type: Schema.Types.Mixed },
            bloom: { type: Schema.Types.Mixed },
            data: { type: Schema.Types.Mixed }
        },
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
