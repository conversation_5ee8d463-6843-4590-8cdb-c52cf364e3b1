"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const campus_location_class_1 = require("./campus-location.class");
const campus_location_model_1 = __importDefault(require("../../models/campus-location.model"));
const campus_location_hooks_1 = __importDefault(require("./campus-location.hooks"));
function default_1(app) {
    const options = {
        Model: (0, campus_location_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/campus-location', new campus_location_class_1.CampusLocation(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('campus-location');
    service.hooks(campus_location_hooks_1.default);
}
exports.default = default_1;
