import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'

export class BonusSetting extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async settle() {
    const now = new Date()
    const utcDate = now.getUTCDate()
    const utcHours = now.getUTCHours()
    const utcMinutes = now.getUTCMinutes()

    if (utcDate == 8 && utcHours == 23 && utcMinutes == 59) {
      const thisMonth8th = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 8, 23, 59, 0, 0))
      const lastMonth8th = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 1, 8, 23, 59, 0, 0))
      let bonusSetting: any = await this.Model.findOne({
        tab: 'earn',
        category: 'ambassador_school',
      })
      if (!bonusSetting) {
        return
      }
      const {accumulated, value: rate} = bonusSetting

      const logs = await this.app.service('commission-log').Model.aggregate([
        {
          $match: {
            tab: 'earn',
            role: 'ambassador_school',
            status: 1,
            isBonusSettle: false,
            isSchool: false,
            actualAt: {
              $gte: lastMonth8th,
              $lt: thisMonth8th,
            },
          },
        },
        {
          $group: {
            _id: '$uid',
            totalValue: {$sum: '$value'},
          },
        },
      ])
      for (let i = 0; i < logs.length; i++) {
        const {_id, totalValue} = logs[i]
        if (totalValue < accumulated) {
          continue
        }
        let logList = await this.app.service('commission-log').Model.find({
          uid: _id,
          tab: 'earn',
          role: 'ambassador_school',
          status: 1,
          isBonusSettle: false,
          isSchool: false,
          actualAt: {
            $gte: lastMonth8th,
            $lt: thisMonth8th,
          },
        })
        let logIds = logList.map((item) => item._id)

        await this.app.service('commission-log').getAddLog({
          uid: _id,
          change: this.calcCommission({amount: totalValue, rate}),
          tab: 'earn',
          source: 'bonus',
          category: 'bonus',
          businessId: `${now.getUTCFullYear()}-${now.getUTCMonth() + 1}-${now.getUTCDate()}`,
          snapshot: logIds,
          status: 1,
          role: 'bonus',
        })
        await this.app.service('commission-log').Model.updateMany({_id: {$in: logIds}}, {$set: {isBonusSettle: true}})
      }
    }
    return
  }

  calcCommission({amount, rate}: any) {
    let commission = 0
    let num = (rate / 100) * amount
    if (!num) {
      commission = 0
    } else if (num <= 1) {
      commission = 1
    } else {
      commission = Math.floor(num)
    }
    return commission
  }
  async cron1({}: any, params?: Params): Promise<any> {
    this.settle()
  }
}
