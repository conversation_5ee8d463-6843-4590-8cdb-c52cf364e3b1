// Initializes the `conf` service on path `/conf`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {Conf} from './conf.class'
import createModel from '../../models/conf.model'
import hooks from './conf.hooks'
import logger from '../../logger'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    conf: Conf & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    multi: ['patch'],
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/conf', new Conf(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('conf')

  service.hooks(hooks)
  // 加载配置到进程中
  setTimeout(() => {
    logger.info('Start load pub conf...')
    service.Model.find({_id: {$in: ['smtp', 'UnitTpl', 'TaskTpl', 'VideoTpl', 'PdUnitTpl', 'PdTaskTpl']}}).then((docs: any) => {
      for (const {_id, val} of docs) {
        app.set(_id, val)
        if (_id === 'smtp') service.redisFn(app, _id, val)
        logger.info('Load pub conf:', _id, val.length)
      }
    })
  }, 1000)
}
