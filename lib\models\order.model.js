"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'order';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        buyer: { type: String, required: true },
        schoolAdmin: { type: String },
        // seller: {type: String}, //seller已废弃,改用sellers
        sellers: { type: Array },
        name: { type: String },
        no: { type: String },
        // link已废弃,改用links
        // link: {
        //   id: {type: String}, // link id, Ex: task.id, unit.id, workshop.id
        //   name: {type: String},
        //   mode: {type: String}, // unit.mode
        //   type: {type: String}, // 2:unit plan; 4:task; 6:evaluation, old.content_type
        //   newId: {type: String},
        //   hash: {type: String},
        // },
        links: [
            {
                id: { type: String },
                name: { type: String },
                mode: { type: String },
                type: { type: String },
                newId: { type: String },
                hash: { type: String },
                cover: { type: String },
                price: { type: Number },
                point: { type: Number },
                style: { type: String },
                goods: { type: Object },
                sessionId: { type: Object },
                count: { type: Object },
                // gift: {type: Boolean}, // 弃用 更换为promotion
                promotion: { type: Boolean },
                giftCount: { type: Number, default: 0 },
                removed: { type: Boolean },
                inviter: { type: String, trim: true },
                schoolInviter: { type: String, trim: true },
                inviteSource: { type: String, trim: true, enum: ['new_prompt', 'sales_follow_up'] },
                inviteSourceId: { type: String, trim: true },
                archived: { type: Boolean, default: false },
                persons: { type: Number, default: 1 },
                packUserTasks: { type: Array },
                oldPackUser: { type: String },
                bookingId: { type: String },
                // premiumCloudUnused: {type: Boolean, default: false}, // 认证精品课快照未使用
                session: { type: String },
                isOnCampus: { type: Boolean, default: false },
                country: { type: String, trim: true },
                city: { type: String, trim: true },
                used: { type: Boolean, default: false },
                refundPrice: { type: Number, default: 0 },
                refundPoint: { type: Number, default: 0 }, // 退款积分
            },
        ],
        /**
         * 订单状态 status 除400外的4xx弃用
         * 100.待支付；
         * 200.支付成功；
         * 300.支付失败；
         * 400.支付超时 Payment has timed out 除400外的4xx弃用
         * 401.未支付 公开课被讲师取消 canceled by the facilitator
         * 402.未支付 公开课因未成团被系统取消 Minimal registration number not met
         * 403.未支付 课件/自学习被下架 Product removed
         * 404.未支付 商品已更新 系统取消
         * 500.已支付 公开课/服务包被购买者取消 canceled by the purchaser
         * 501.已支付 公开课被讲师取消 canceled by the facilitator
         * 502.已支付 公开课因未成团被系统取消 Minimal registration number not met
         * 503.已支付 支付前被下架/删除,支付后立即退款
         */
        status: { type: Number, default: 100 },
        settled: { type: Boolean, default: false },
        /**
         * 订单类型
         * unit
         * session_public
         * session_self_study
         * session_service_pack 捆绑服务包
         * service_pack 服务包
         * service_premium 主题服务包
         * service_substitute 代课服务包
         * premium_cloud 认证精品课快照
         * prompt
         */
        type: {
            type: String,
            enum: [
                'unit',
                'session_public',
                'session_self_study',
                'session_service_pack',
                'service_pack',
                'service_premium',
                'service_substitute',
                'premium_cloud',
                'prompt',
            ],
        },
        price: { type: Number },
        point: { type: Number },
        // subtotal: {type: Number}, // Unit cent 商品总金额 后续增加
        // cash: {type: Number}, // Unit cent 现金支付 后续增加
        // giftCard: { type: Number }, // Unit cent gift card 支付 后续增加
        // coupon: { type: Number }, // Unit cent 优惠金额 后续增加
        payMethod: { type: Array },
        paid: { type: Number, default: 0 },
        paypalId: { type: String },
        braintreeId: { type: String },
        /**
         * 支付信息 paymentInfo
         * {
         *  paymentInstrumentType string 支付方式
         *  cardType string 卡机构
         *  last4 string 卡号后四位
         * }
         */
        paymentInfo: { type: Object },
        expiration: { type: Date },
        // 退款详情
        refund: [
            {
                method: { type: String },
                status: { type: Number },
                amount: { type: Number },
                executed: { type: Boolean, default: true },
                createdAt: { type: Date },
                executedAt: { type: Date }, //退款执行时间
            },
        ],
        paidAt: { type: Date },
        reminder: { type: Number, default: 0 },
        inviter: { type: String, trim: true },
        schoolInviter: { type: String, trim: true },
        inviteSource: { type: String, trim: true, enum: ['new_prompt', 'sales_follow_up'] },
        inviteSourceId: { type: String, trim: true },
        isPoint: { type: Boolean, default: false },
        isSeparated: { type: Boolean, default: false },
        isTicket: { type: Boolean, default: false },
        isSchool: { type: Boolean, default: false },
        sharedSchool: { type: String },
        servicePremium: { type: String },
        servicePremiumSnapshot: { type: Object },
        persons: { type: Number, default: 1 },
        servicePackApply: { type: String }, // 主题服务包申请id
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
