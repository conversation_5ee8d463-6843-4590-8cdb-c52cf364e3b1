"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const mod = {
    // Deadline reached, auto set session status to close
    // async sessionAutoClose() {
    //   const utc = new Date().toISOString()
    //   let rs = await knex('class').whereNotNull('deadline').whereNot('status', 'close').where('deadline', '<', utc).select(['id'])
    //   if (rs.length === 0) return 0
    //   const ids = rs.map((v: any) => v.id)
    //   return await knex('class').whereIn('id', ids).update({status: 'close'})
    // },
    };
    // app.all('/cron/:min', async (req: Request, res: Response) => {
    //   const {min} = req.params
    //   const utc = new Date().toISOString()
    //   const conf: any = {1: ['sessionAutoClose']}
    //   const rs: any = {min, utc}
    //   for (const key of conf[min]) {
    //     rs[key] = await mod[key]()
    //   }
    //   res.json(rs)
    // })
}
exports.default = default_1;
