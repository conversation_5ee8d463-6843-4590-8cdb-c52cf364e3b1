"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            hook_1.default.sysQuery('buyer'),
            async (d) => {
                await d.service.goodsFilter(d.params.query, d.params.user);
            },
        ],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                var _a;
                const uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                const { goodsId, style = 'unit' } = d.data;
                let cart = await d.service.Model.findOne({ buyer: uid, goodsId });
                if (cart) {
                    return Promise.reject(new GeneralError('Existed in cart'));
                }
                let order = await d.app.service('order').Model.find({
                    buyer: uid,
                    status: { $in: [100, 200, 600] },
                    links: { $elemMatch: { id: { $in: [goodsId] }, removed: { $exists: false } } },
                });
                if (order.length > 0) {
                    return Promise.reject(new GeneralError('Existed in order'));
                }
                let goods;
                if (style == 'unit') {
                    goods = await d.app.service('unit').ext(await d.app.service('unit').Model.findOne({ _id: goodsId }).select(d.app.service('unit').selectList).lean());
                }
                else if (style == 'session') {
                    goods = await d.app.service('session').Model.findOne({ _id: goodsId }).select(d.app.service('session').selectList).lean();
                }
                if (!goods || (style === 'unit' && !goods.publish.lib)) {
                    return Promise.reject(new GeneralError({ message: 'Link not found or unpublish', goods: goodsId }));
                }
                d.data.style = style;
                d.data.buyer = uid;
                d.data.goods = goods;
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
