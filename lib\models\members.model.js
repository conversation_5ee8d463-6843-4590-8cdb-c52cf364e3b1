"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'members';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String, required: true },
        uid: { type: String, required: true },
        nickname: { type: String, required: true },
        avatar: { type: String, required: true },
        email: { type: String, required: true },
        role: { type: Number, default: 1 },
        attend: { type: Boolean, default: false },
        block: { type: Boolean, default: false }
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
