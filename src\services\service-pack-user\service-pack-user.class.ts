import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'
import {BadRequest, NotFound} from '@feathersjs/errors'

export class ServicePackUser extends Service {
  app: Application
  selectList: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.selectList = ['-snapshot.linkSnapshot', '-snapshot.feedback', '-snapshot.approval', '-snapshot.attachments']
    this.app = app
  }
  toInfo(_id: String, params?: Params) {
    return this.Model.findById(_id).select(this.selectList.join(' '))
  }
  // 通过服务包ID查已购买的服务包
  async getByPack({pack}: any, params: Params) {
    const doc = Acan.clone(await this.Model.findOne({uid: params.user?._id, 'snapshot._id': pack}))
    if (!doc) return Promise.reject(new NotFound())
    await this.extUser(doc)
    return doc
  }
  // need remove
  getBuyByOrder(query: any, params: Params) {
    return this.buyByOrder(query, params)
  }
  // Lecture包按次数删除tasks
  async taskRemove({packUser, times}: any) {
    const {tasks}: any = await this.Model.findById(packUser).select(['tasks'])
    if (Acan.isEmpty(tasks)) return
    return tasks.splice(0, times)
  }
  // Lecture包补买插入顺序
  taskInsert({packUserDoc, taskIds}: any) {
    const list = []
    for (const id of packUserDoc.tasks) {
      list.push({id, index: packUserDoc.taskIndex.indexOf(id)})
    }
    for (const id of taskIds) {
      list.push({id, index: packUserDoc.taskIndex.indexOf(id)})
    }
    Acan.arrSort(list, 'index')
    return list.map((v) => v.id)
  }
  // 认证精品课的上课列表，顺序处理逻辑 List of classes for certified premium courses, sequential processing logic
  taskSort(authDoc: any) {
    const {linkGroup, link} = authDoc.unitSnapshot
    const list: any = []
    // 组排序 Group sorting
    for (const group of linkGroup) {
      // 组内排序 Sort within group
      for (const o of link) {
        if (o.group !== group._id) continue
        if (!list.includes(o.id)) list.push(o.id) // 去重 Remove duplicates
      }
    }
    return list
  }
  // 主题服务包购买处理
  async contentOrientatedCreate({pid, order, contentOrientated, isPoint, servicePremium}: any, params: Params) {
    const uid = params.user?._id
    const rs = []
    for (const one of contentOrientated) {
      // let pack: any
      // if (one.servicePack) pack = await this.app.service('service-pack').Model.findById(one.servicePack)
      // 获取认证的精品课快照数据 Get snapshot data of certified premium courses
      const authRs: any = await this.app.service('service-auth').Model.findById(one.premium)
      const post: any = {
        uid,
        order,
        pid,
        // price: one.price,
        isPoint,
        premium: one.premium,
        servicePremium,
        // total: one.times,
      }
      if (authRs) {
        post.snapshot = Acan.clone(authRs)
        //  Lecture包 需要计算出所有课件的排序 The Lecture package needs to calculate the order of all the courseware
        post.taskIndex = this.taskSort(post.snapshot)
        post.tasks = post.taskIndex
      }
      rs.push(Acan.clone(await this.create(post)))
    }
    return rs
  }
  // 代课服务包创建子服务包
  async substituteCreate({packId, pid, order, country, city, isPoint}: any, params: Params) {
    const uid = params.user?._id
    let packUserSubstitute: any = await this.app.service('service-pack-user').Model.findOne({uid, pid, country, city})
    if (packUserSubstitute) {
      return {
        packUserSubstitute,
        isNew: false,
      }
    } else {
      const pack: any = await this.app.service('service-pack').Model.findById(packId)
      const post: any = {
        uid,
        order,
        pid,
        isPoint,
        snapshot: pack,
        country,
        city,
      }
      let packUserSubstitute: any = await this.create(post)
      return {
        packUserSubstitute,
        isNew: true,
      }
    }
  }

  // 支付后成功创建用户购买的服务包，内部调用
  async buyByOrder({packId, order, session, price, point = 0, isPoint = false}: any, params: Params) {
    const uid = params.user?._id
    const pack: any = await this.app.service('service-pack').Model.findById(packId)
    const sessionDoc: any = await this.app.service('session').Model.findById(session).select(['name', 'start', 'end'])
    const post: any = {
      uid,
      order,
      snapshot: Acan.clone(pack),
      price,
      point,
      isPoint,
    }
    if (sessionDoc) post.session = Acan.clone(sessionDoc)
    if (pack.serviceRoles === 'service-task') {
      post.associatedTaskStatus = 'unassigned'
    }
    logger.log('pdjfdjfj', post)
    Acan.objClean(post)
    logger.log('pdjfdjfj1', post)
    if (pack.associatedTask) {
      post.associatedTask = Acan.clone(pack.associatedTask)
    }
    logger.log('pdjfdjfj2', post)
    // 更新服务包销量    Update service package sales
    await this.app.service('service-pack').incCount(packId, {'count.sold': 1, 'count.valid': 1})
    const rs: any = Acan.clone(await this.create(post))
    try {
      if (pack.serviceRoles === 'service-task') {
        for (let index = 0; index < pack.sections.length; index++) {
          const element = pack.sections[index]
          const curSection: any = {
            sectionNo: index + 1,
            name: element.name,
            prompt: element.prompt,
            salesPrice: element.salesPrice,
            costPrice: element.costPrice,
            serviceTaskId: rs._id,
            uid,
            credit: [
              {
                userId: uid,
                points: element.salesPrice,
              },
            ],
            taskDetails: {
              name: pack.name,
              cover: pack.cover,
              mentoringType: pack.mentoringType,
            },
          }
          await this.app.service('section').create(curSection)
        }
        const updatedParent = await this.Model.findOneAndUpdate(
          {
            'associatedTask._id': packId,
            order: order,
          },
          {
            $set: {'associatedTask.childTask': rs._id},
          },
          {
            new: true,
            projection: '_id',
          }
        )

        if (updatedParent) {
          await this.Model.updateOne({_id: rs._id}, {$set: {parentAssociatedTask: updatedParent._id}})
        }
        logger.log('djjfjfjjf11', updatedParent, order)
      }
    } catch (err: any) {
      throw new Error('Error in creating sections:' + err.message)
    }
    logger.log('djjfjfjjf', rs)
    if (pack.contentOrientatedEnable && !Acan.isEmpty(pack.contentOrientated)) {
      // 批量创建主题服务包下的Lecture包
      rs.childs = await this.contentOrientatedCreate(
        {
          order,
          pid: rs._id,
          contentOrientated: pack.contentOrientated,
          isPoint,
        },
        params
      )
    }
    return rs
  }
  // 判断服务包次数是否足够
  async checkTimes({_id, times}: any, params: Params) {
    const one: any = await this.Model.findById(_id).select(['total', 'used'])
    if (one.used + times > one.total) return Promise.reject(new Error('Not enough times'))
    return true
  }
  async extUser(one: any) {
    one.owner = await this.app.service('users').uidToInfo(one.uid)
    if (!one.owner) {
      await this.app.service('school-plan').Model.findOne({_id: one.uid})
    }
    if (one.snapshot.uid) {
      one.snapshot.owner = await this.app.service('users').uidToInfo(one.snapshot.uid)
    }
  }
  async getCheckFree({ids}: any, params: Params) {
    let res: any = {}
    for (let i = 0; i < ids.length; i++) {
      const _id = ids[i]
      let data: any = await this.app.service('service-pack-user-data').Model.find({packUser: _id, payMethod: {$in: ['cash', 'point']}})
      let childData: any = await this.app.service('service-pack-user-data').Model.find({pid: _id, payMethod: {$in: ['cash', 'point']}})
      if (data.length > 0 || childData.length > 0) {
        res[_id] = false
      } else {
        res[_id] = true
      }
    }
    return {res}
  }

  // 子服务包状态变化，检查主服务包状态
  async checkMaterPackUser(pid: String) {
    // 子服务包失效的情况
    const doc: any = await this.Model.findById(pid)
    const count = await this.Model.count({pid, status: true})
    if (count === 0 && doc.status === true) {
      // 所有的子服务包已经失效，需要更新主服务包的状态为失效
      await this.Model.updateOne({_id: pid}, {$set: {status: false}})
      await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': -1})
    } else if (count > 0 && doc.status === false) {
      // 子服务包存在有效，需要更新主服务包的状态为激活
      await this.Model.updateOne({_id: pid}, {$set: {status: true}})
      await this.app.service('service-pack').incCount(doc.snapshot._id, {'count.valid': 1})
    }
  }

  // 更新地址,经纬度
  async patchLocation({_id, place_id, address}: any, params: Params) {
    // 改学校管理员可更新
    const uid = params.user?._id
    let packUserData: any = await this.Model.findOne({_id})
    let schoolUser: any = await this.app.service('school-user').Model.findOne({uid, school: packUserData.uid})
    if (!schoolUser || !schoolUser.role.includes('admin')) {
      return Promise.reject(new Error('You are not authorized to update this service pack'))
    }

    this.app.service('campus-location').updateLocation({_id, place_id, model: 'service-pack-user'})
    return await this.Model.updateOne({_id}, {place_id, address})
  }
}
