import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {NotFound, BadRequest} from '@feathersjs/errors'

export class Section extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async getDetails(_id: string, params?: Params) {
    const section = await this.Model.findById(_id)
    if (!section) throw new NotFound('Section not found')
    return section
  }

  async create(data: any, params?: Params) {
    return super.create(data, params)
  }

  async patch(_id: string, data: any, params?: Params) {
    if (data.serviceProviderId) {
      return this.addUserInfoToProvider(_id, data, params!)
    } else if (data.lastOne) {
      return this.updateLastSection(_id, data, params!)
    }

    const result = await super.patch(_id, data, params)

    // Check if section was marked as completed and send notification
    if (data.markAsCompleted === true || data.status === 'completed') {
      const section: any = await this.Model.findById(_id)
      if (section?.serviceTaskId) {
        await this.sendSectionCompletionNotification(section.serviceTaskId, params!)
      }
    }

    return result
  }

  async addUserInfoToProvider(id: string, data: {serviceProviderId: string}, params: Params) {
    const userId = params.user?._id || params.user?.id
    if (!userId) throw new Error('User not authenticated')
    const user = await this.app.service('users').get(data.serviceProviderId)

    const newProvider = {
      userId: data.serviceProviderId,
      name: user.name || [],
      nickname: user.nickname || '',
      avatar: user.avatar || '',
      assignedTime: new Date(),
    }

    return await this.Model.findByIdAndUpdate(id, {$push: {serviceProviderDetails: newProvider}}, {new: true})
  }

  async extUser(result: any, _params: Params) {
    if (result.uid) result.bookerInfo = await this.app.service('users').uidToInfo(result.uid)
    if (result.servicer) result.servicerInfo = await this.app.service('users').uidToInfo(result.servicer)
    return result
  }

  async updateLastSection(id: string, data: {lastOne: boolean; curServicer: string; bookingId: string}, params: Params) {
    try {
      await Promise.all([
        this.Model.updateMany({serviceTaskId: id, completedBy: null}, {$set: {completedBy: data.curServicer}}),
        this.app.service('service-pack-user').Model.updateOne({_id: id}, {associatedTaskStatus: 'completed'}),
        this.app.service('service-booking').Model.updateOne({_id: data.bookingId}, {completedTime: new Date()}),
      ])

      // Send notification to the booker/student about section completion
      await this.sendSectionCompletionNotification(id, params)
    } catch (error) {
      throw new BadRequest('Failed to update the last section')
    }
  }

  async sendSectionCompletionNotification(serviceTaskId: string, params: Params) {
    try {
      // Get the service-pack-user data to find the booker and service details
      const servicePackUser: any = await this.app.service('service-pack-user').Model.findById(serviceTaskId)
      if (!servicePackUser) return

      const bookerId = servicePackUser.uid
      const serviceType = servicePackUser.snapshot?.type || 'service'
      const associatedTaskName = servicePackUser.snapshot?.name || 'Associated Task'

      const booker: any = await this.app.service('users').Model.findOne({_id: bookerId})
      if (!booker) return

      let url = ''
      if (booker.roles?.includes('student')) {
        url = `${SiteUrl}/v2/study/purchased?tab=myAssociatedTask&subtab=ongoing`
      } else if (booker.roles?.includes('teacher')) {
        url = `${SiteUrl}/v2/home/<USER>
      } else {
        url = `${SiteUrl}/v2/study/purchased?tab=myAssociatedTask&subtab=ongoing`
      }

      // Send notification email
      await this.app.service('notice-tpl').mailto(
        'AssociatedTaskSectionCompleted(ByProvider)',
        booker,
        {
          username: booker?.nickname || 'User',
          service_type: serviceType,
          associated_task_name: associatedTaskName,
          url,
        },
        params.user?._id
      )
    } catch (error) {
      console.error('Failed to send section completion notification:', error)
    }
  }
}
