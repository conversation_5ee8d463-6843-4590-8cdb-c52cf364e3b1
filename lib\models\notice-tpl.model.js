"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'noticeTpl';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        code: { type: String, trim: true, required: true, unique: true },
        name: { type: String, trim: true },
        enable: { type: Boolean, default: true },
        enableInbox: { type: Boolean, default: false },
        enablePush: { type: Boolean, default: false },
        enableSms: { type: Boolean, default: false },
        title: { type: String, trim: true },
        text: { type: String, trim: true },
        note: { type: String, trim: true },
        sms: { type: String, trim: true },
        keys: { type: [String], trim: true },
        category: { type: String, trim: true }, //类目ID
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
