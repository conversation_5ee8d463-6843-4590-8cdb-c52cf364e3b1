{"name": "learn-api", "description": "", "version": "0.0.0", "homepage": "", "private": true, "main": "src", "keywords": ["feathers"], "author": {}, "contributors": [], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "engines": {"node": ">=14.16.0", "yarn": ">= 0.18.0"}, "scripts": {"test": "yarn run lint && yarn run compile && yarn run jest", "lint": "eslint src/. test/. --config .eslintrc.json --ext .ts --fix", "devDraw": "export NODE_ENV=dev && ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/draw.ts", "draw": "export NODE_ENV=production && ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/draw.ts", "dev": "export NODE_ENV=dev && ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/", "devWin": "set NODE_ENV=production&& ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/", "dev2": "export NODE_ENV=dev2 && echo \"********** redis api3 mongo\" >> /etc/hosts && ts-node-dev --transpile-only --exit-child --no-notify src/", "dev3": "export NODE_ENV=dev3 && ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/", "devl": "set NODE_ENV=devLocal&& ts-node-dev --transpile-only --clear --no-notify src/", "line": "export NODE_ENV=production && echo \"********** redis\" >> /etc/hosts && ts-node-dev --transpile-only --exit-child --respawn --clear --no-notify src/", "start": "node lib/", "prodTest": "export NODE_ENV=test && whoami && echo \"********** redis\" >> /etc/hosts && node lib/", "prod": "export NODE_ENV=production && whoami && echo \"********** redis\" >> /etc/hosts && node lib/", "jest": "jest --force<PERSON>xit", "db": "", "compile": "shx rm -rf lib/ && tsc"}, "standard": {"env": ["jest"], "ignore": []}, "types": "lib/", "dependencies": {"@feathersjs/authentication": "^4.5.11", "@feathersjs/authentication-client": "^4.5.15", "@feathersjs/authentication-local": "^4.5.11", "@feathersjs/authentication-oauth": "^4.5.11", "@feathersjs/cli": "^4.7.0", "@feathersjs/configuration": "^4.5.11", "@feathersjs/errors": "^4.5.11", "@feathersjs/express": "^4.5.11", "@feathersjs/feathers": "^4.5.11", "@feathersjs/socketio": "^4.5.11", "@feathersjs/socketio-client": "^4.5.15", "@feathersjs/transport-commons": "^4.5.12", "@seald-io/nedb": "^2.2.0", "braintree": "^3.21.0", "bson": "^4.6.1", "compression": "^1.7.4", "cors": "^2.8.5", "express-fileupload": "^1.4.0", "feathers-knex": "^8.0.1", "feathers-mongodb-fuzzy-search": "^2.0.1", "feathers-mongoose": "^8.5.1", "feathers-nedb": "^6.0.0", "feathers-sync": "^3.0.1", "file-type": "^18.0.0", "fix-esm": "^1.0.1", "google-images": "^2.1.0", "googleapis": "^107.0.0", "got-cjs": "^12.5.4", "helmet": "^4.6.0", "imap": "^0.8.19", "knex": "^1.0.4", "mongodb-core": "^3.2.7", "mongoose": "^6.1.4", "mqtt": "^4.3.7", "mysql2": "^2.3.3", "node": "^14.19.0", "node-fetch": "2", "nodemailer": "^6.7.8", "pdfkit-table": "^0.1.99", "plivo": "^4.67.0", "proxy-agent": "^5.0.0", "redis": "^4.0.4", "serve-favicon": "^2.5.0", "socket.io-parser": "^4.1.2", "tracer": "^1.1.6"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/jest": "^27.4.0", "@types/jsonwebtoken": "^8.5.8", "@types/node-fetch": "^2.6.1", "@types/serve-favicon": "^2.5.3", "@types/socket.io-parser": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "axios": "^0.26.0", "cross-env": "^10.0.0", "eslint": "^8.6.0", "jest": "^27.5.1", "shx": "^0.3.4", "ts-jest": "^27.1.3", "ts-node-dev": "^2.0.0", "typescript": "^4.5.4"}}