"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const { google } = require('googleapis');
const bson_1 = require("bson");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = __importDefault(require("../logger"));
const scopes = [
    'https://www.googleapis.com/auth/drive.file',
    // 'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/presentations',
    // 'profile email',
    // 'https://www.googleapis.com/auth/presentations.readonly',
    'https://www.googleapis.com/auth/script.external_request',
    'https://www.googleapis.com/auth/script.container.ui',
];
function default_1(app) {
    const authConf = app.get('googleAuth');
    const oauth2Client = new google.auth.OAuth2(...authConf);
    const googleAuth = async (req, res) => {
        var _a;
        const { prompt, type = 'login', state } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        // 'online' (default) or 'offline' (gets refresh_token)
        const opt = { access_type: 'offline' };
        const scope = ['profile email']; // 普通登录权限
        // slides登录权限
        if (type === 'slide') {
            opt.prompt = 'consent';
            scope.push(...scopes);
        }
        if (prompt)
            opt.prompt = 'consent';
        opt.scope = scope;
        if (state)
            opt.state = state;
        logger_1.default.info(scope);
        const url = oauth2Client.generateAuthUrl(opt);
        return res.redirect(url);
    };
    app.all('/google/test', async (req, res, next) => {
        res.json({});
    });
    app.all('/google/auth', googleAuth);
    app.all('/google/authCall', async (req, res, next) => {
        var _a;
        const redis = app.get('redis');
        const userModel = app.service('users').Model;
        let { code, scope, state } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        try {
            state = JSON.parse(state || null);
        }
        catch (error) { }
        // 通过code获取google登录授权
        const { tokens, response } = await oauth2Client.getToken(code).catch((e) => {
            // logger.info(e)
            return e;
        });
        if (!tokens) {
            const { status, data } = response !== null && response !== void 0 ? response : {};
            logger_1.default.info(status, data);
            return res.json({ ...data, status });
        }
        let info = jsonwebtoken_1.default.decode(tokens.id_token);
        let { sub, name, picture, given_name, family_name, locale, exp, email } = info;
        oauth2Client.setCredentials(tokens);
        const conf = { type: 'google', locale, email, avatar: picture, name, fname: [given_name, family_name], exp: tokens.expiry_date };
        logger_1.default.info(tokens.scope, conf);
        redis.set(`googleAuth:${sub}`, JSON.stringify({ ...conf }));
        if (tokens.scope.includes('auth/presentations')) {
            // slide 附加授权 需要保存 refresh_token
            // for slide save token
            conf.scope = tokens.scope;
            conf.token = tokens.access_token;
            if (tokens.refresh_token)
                conf.rt = tokens.refresh_token;
            redis.set(`googleRT:${sub}`, conf.rt);
        }
        else {
            // normal login need clean token and exp
            conf.token = '';
            conf.exp = Date.now();
        }
        await app.service('user-token').Model.updateOne({ sub }, { $set: conf }, { upsert: true });
        let user = Acan.clone(await userModel.findOne({ google: sub }));
        const post = { last: new Date() };
        // 如果邮箱已经存在则直接绑定google授权
        if (!user && email) {
            user = Acan.clone(await userModel.findOne({ email }));
            post.google = sub;
        }
        if (!user) {
            // 未注册的google
            if (state.uid) {
                // 绑定google到指定帐号上
                user = Acan.clone(await userModel.findOne({ _id: state.uid }));
                const $set = { google: sub };
                if (user) {
                    if (!user.email && email)
                        $set.email = email;
                    await userModel.updateOne({ _id: state.uid }, { $set });
                }
                return res.json({ ...state, ...$set });
            }
            else if (email && state.type === 'signup') {
                // 注册用户
                logger_1.default.warn(user, info);
                const _id = new bson_1.ObjectID();
                user = Acan.clone(await app.service('users').create({
                    _id,
                    uid: _id.toString(),
                    roles: [state.role],
                    avatar: picture,
                    email,
                    nickname: name,
                    name: [given_name, family_name],
                    google: sub,
                    last: new Date(),
                    inviter: state.inviter || '',
                }));
            }
            else {
                return res.json({ message: 'Account does not exist', code: 404 });
            }
        }
        else {
            // 登录后更新最后登录时间
            if (Acan.isEmpty(user.name))
                post.name = [given_name, family_name];
            await userModel.updateOne({ _id: user._id }, { $set: post });
            Object.assign(user, post);
        }
        // 同步登录授权
        const role = user.roles.includes('student') ? 'student' : 'teacher';
        const auth = await app.service('authentication').create({ strategy: 'sync' }, {
            payload: { sid: null, role },
            user: { ...user, id: user._id },
        });
        // addon 插件端登陆用
        if (state.key) {
            console.log('addon login key', state.key, Object.keys(auth), auth.accessToken);
            await app.get('redis').set(`addonKey:${state.key}`, auth.accessToken);
        }
        res.json(auth);
    });
    app.all('/google/cache', async (req, res) => {
        var _a;
        const redis = app.get('redis');
        const rs = await redis.keys('googleAuth:*');
        const list = {};
        const nt = Math.ceil(Date.now() / 1000);
        for (const key of rs) {
            const googleId = key.split(':').pop();
            const { exp, name } = JSON.parse((_a = (await redis.get(key))) !== null && _a !== void 0 ? _a : '{}');
            list[key] = { name, exp: new Date(exp) };
        }
        res.json(list);
    });
}
exports.default = default_1;
