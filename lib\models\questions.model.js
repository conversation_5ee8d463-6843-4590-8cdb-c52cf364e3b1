"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'questions';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    // const outline1 = new Schema({
    //   name: {type: String, trim: true},
    //   enable: {type: Boolean, default: false},
    //   score: {type: Number, trim: true},
    // })
    // const outline = new Schema({
    //   name: {type: String, trim: true},
    //   enable: {type: Boolean, default: false},
    //   score: {type: Number, trim: true},
    //   child: [outline1],
    // })
    const schema = new Schema({
        uid: { type: String },
        id: { type: String, required: true, trim: true },
        page: { type: String, required: true, trim: true },
        type: { type: String, trim: true, enum: Agl.questionsTypes },
        multi: { type: Boolean, default: false },
        bloom: { type: Number },
        dimension: { type: Number },
        verb: [{ type: String }],
        tags: [{ type: String }],
        tips: { type: String, trim: true },
        data: { type: String, trim: true },
        score: {
            // score config
            outline: {
                val: { type: Number },
                enable: { type: Boolean, default: false },
                rubric: { type: Boolean, default: false },
                criteria: { type: Schema.Types.Mixed }, // criteria data
            },
            assess: {
                val: { type: Number },
                enable: { type: Boolean, default: false },
                rubric: { type: Boolean, default: false },
                criteria: { type: Schema.Types.Mixed },
            },
            pd: {
                val: { type: Number },
                enable: { type: Boolean, default: false },
                rubric: { type: Boolean, default: false },
                criteria: { type: Schema.Types.Mixed },
            },
            goal: {
                val: { type: Number },
                enable: { type: Boolean, default: false },
                rubric: { type: Boolean, default: false },
                criteria: { type: Schema.Types.Mixed },
            },
            skills: {
                val: { type: Number },
                enable: { type: Boolean, default: false },
                rubric: { type: Boolean, default: false },
                criteria: { type: Schema.Types.Mixed },
            },
        },
        outlines: {
            outline: { type: Schema.Types.Mixed },
            assess: { type: Schema.Types.Mixed },
            pd: { type: Schema.Types.Mixed },
            goal: { type: Schema.Types.Mixed },
            skills: { type: Schema.Types.Mixed }, // skills
        },
        options: [
            {
                // options
                val: { type: String, trim: true },
                on: { type: Boolean, default: false }, // choice answer
            },
        ],
        list: [
            {
                // multi questions for new text
                bloom: { type: Number },
                dimension: { type: Number },
                verb: [{ type: String }],
                tags: [{ type: String }], // knowledge tags
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
