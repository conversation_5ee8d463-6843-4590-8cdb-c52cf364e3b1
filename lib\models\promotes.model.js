"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'promotes';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        name: { type: String, required: true },
        type: { type: String, required: true },
        pv: { type: Number, default: 0 },
        signup: { type: [String] },
        login: { type: [String] },
        pay: [{
                uid: { type: String, required: true },
                order: { type: String, required: true },
                amount: { type: Number, required: true }, // 金额
            }],
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
