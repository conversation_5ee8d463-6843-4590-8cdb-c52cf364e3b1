"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'unit';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        mode: { type: String, required: true, default: 'task', enum: Agl.unitMode },
        uid: { type: String, required: true },
        name: { type: String, trim: true },
        del: { type: Boolean, default: false },
        guest: { type: <PERSON><PERSON><PERSON>, default: false },
        orderId: { type: String },
        publish: {
            lib: { type: Boolean, default: false },
            link: { type: Boolean, default: false },
            // study: {type: Boolean, default: false}, // to self-study
            sales: { type: Number, default: 0 },
            date: { type: Date }, // publish date
        },
        video: { type: String },
        curriculum: { type: String, required: true },
        rubricsCriteria: { type: Schema.Types.Mixed },
        tpl: { type: String, trim: true },
        overview: { type: String, trim: true },
        cover: { type: String, trim: true },
        type: { type: String, enum: Agl.unitType },
        service: {
            type: { type: [String] },
            participants: { type: String, sparse: true, enum: Agl.subjectsParticipants }, // participants
        },
        source: { type: String, trim: true },
        sourceUid: { type: String, trim: true },
        discount: {
            val: { type: Number, default: 0 },
            price: { type: Number, default: 0 },
            end: { type: Date },
            size: { type: Number, default: 0 }, // group size
        },
        filled: { type: Boolean, default: false },
        // --- task start ---
        pid: { type: String, sparse: true },
        sid: { type: String, sparse: true, trim: true },
        sessionType: { type: String, enum: Agl.unitSessionType },
        pageNum: { type: Number, sparse: true, default: 0 },
        question1: { type: String, sparse: true, trim: true },
        question2: { type: String, sparse: true, trim: true },
        question3: { type: String, sparse: true, trim: true },
        isEdit: { type: Boolean, sparse: true, default: false },
        oldId: { type: String, sparse: true, unique: true },
        // --- task end ---
        // --- unit start ---
        subjects: [
            {
                // publish subjects
                label: { type: String, sparse: true, trim: true },
                value: { type: String, sparse: true, trim: true },
            },
        ],
        outlineSubjects: [{ type: String }],
        grades: [
            {
                label: { type: String, sparse: true, trim: true },
                value: { type: String, sparse: true, trim: true },
            },
        ],
        // grade: {type: [Number], sparse: true, trim: true}, // [min, max]
        unit: { type: String, sparse: true, trim: false },
        duration: {
            value: { type: Number, sparse: true },
            unit: { type: String, sparse: true, trim: false },
        },
        idea: { type: String, sparse: true, trim: false },
        words: { type: [String], sparse: true, trim: true },
        inquiry: { type: [String], sparse: true, trim: true },
        goals: { type: [String], sparse: true, trim: true },
        connection: { type: Schema.Types.Mixed, sparse: true },
        linkGroup: [
            {
                name: { type: String, trim: true },
                alias: { type: String, trim: true },
            },
        ],
        // --- unit end ---
        // --- Assessment tool ---
        toolType: { type: String, enum: Agl.unitToolType, sparse: true },
        toolSource: { type: String },
        toolCount: {
            teacher: { type: Number, default: 0 },
            self: { type: Number, default: 0 },
            others: { type: Number, default: 0 },
        },
        toolGroup: [
            {
                name: { type: String, trim: true },
                tag: { type: String, trim: true },
                index: { type: [String] },
                teacher: { type: Boolean, default: true },
                self: { type: Boolean, default: true },
                peer: { type: Boolean, default: true },
                anonymous: { type: Boolean, default: true },
                visible: { type: String, enum: Agl.unitToolGroupVisible },
                mark: { type: String, trim: true },
                type: { type: String, enum: Agl.unitToolDataType, trim: true },
                options: { type: [String], trim: true },
            },
        ],
        toolData: [
            {
                group: { type: String, required: true },
                name: { type: String, trim: true },
                mark: { type: String, trim: true },
                type: { type: String, enum: Agl.unitToolDataType, trim: true },
                options: { type: [String], trim: true },
            },
        ],
        // --- Assessment tool end ---
        ext: { type: Schema.Types.Mixed },
        linkNum: { type: Number, default: 0 },
        link: [
            {
                id: { type: String, required: true },
                mode: { type: String, required: true },
                group: { type: String },
            },
        ],
        premiumAuth: { type: Boolean, default: false },
        snapshot: { type: Schema.Types.Mixed },
        template: { type: Schema.Types.Mixed },
        tags: { type: Schema.Types.Mixed },
        reflection: { type: Schema.Types.Mixed },
        income: { type: Number, default: 0 }, //收入总计,单位分
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
