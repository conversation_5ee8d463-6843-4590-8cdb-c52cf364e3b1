"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    app.get('/active_parent/:id', async (req, res) => {
        var _a;
        const { id } = (_a = req.params) !== null && _a !== void 0 ? _a : {};
        if (id) {
            await app.service('students').Model.updateOne({ _id: id }, { 'parent.status': 2 });
        }
        res.redirect(SiteUrl.toString());
    });
}
exports.default = default_1;
