{
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": ["source.fixAll.eslint"],
  "search.exclude": {
    "yarn.lock": true,
    "lib/": true,
    "public/": true
  },
  "files.exclude": {
    // "lib/": true
  },
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"],
  "i18n-ally.localesPaths": ["src/i18n"]
}
