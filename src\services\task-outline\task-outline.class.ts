import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'

export class TaskOutline extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async getByRid({_id}: {_id: string}, params: Params): Promise<any> {
    return Acan.clone(await this.Model.findOne({task: _id}))
  }
  async copy([old, nid]: any, params: Params): Promise<any> {
    const doc = Acan.clone(await this.Model.findOne({task: old}))
    if (!doc) return {}
    for (const k of ['_id', '__v']) {
      delete doc[k]
    }
    doc.task = nid
    return await this.Model.create(doc)
  }
  async unitServiceSet(data: any, $set: any) {
    let pdKey = null
    for (const key of Object.keys(data)) {
      if (key.includes('.curr') && data[key] === 'pd') pdKey = key.split('.')[0]
    }
    if (!pdKey) return
    const subjectIds = []
    for (const key of Object.keys(data[`${pdKey}.data`])) {
      const _id = key.split(':')?.[1]
      if (_id) subjectIds.push(_id)
    }
    $set['service.type'] = subjectIds
    $set['service.participants'] = await this.app.service('subjects').idToParticipants(subjectIds)
  }
}
