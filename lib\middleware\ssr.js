"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
function default_1(app) {
    const metaTpl = (data) => {
        return `<!DOCTYPE html><html lang="en"><head>
<title>${data.title} - Classcipe</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="${data.desc}">
<meta name="keywords" content="${data.keywords}">
<meta name="ogUrl" content="${data.url}">
<meta name="ogImage" content="${data.image}">
<meta name="ogTitle" content="${data.title}">
<meta name="ogDescription" content="${data.desc}">
<meta property="og:site_name" content="Classcipe" />
<meta property="og:title" content="${data.title}" />
<meta property="og:type" content="article" />
<meta property="og:url" content="${data.url}" />
<meta property="og:image" content="${data.image}" />
<meta property="og:description" content="${data.desc}" />
<meta name="twitter:card" content="article">
<meta name="twitter:site" content="@classcipe">
<meta name="twitter:url" content="${data.url}">
<meta name="twitter:title" content="${data.title}">
<meta name="twitter:description" content="${data.desc}">
<meta name="twitter:image" content="${data.image}">
<meta name="twitter:description" content="${data.desc}">
</head>
<body>${data.body || ''}</body></html>`;
    };
    app.get('/ssr/unit/:id', async (req, res) => {
        var _a, _b;
        const ua = req.headers['user-agent'];
        const { id } = req.params;
        logger_1.default.info('ssr', req.url, req.hostname, req.protocol, id, ua);
        const rs = (await app.service('unit').get(id, { query: { 'publish.lib': true } })) || {};
        res.set('Content-Type', 'text/html');
        if (!rs)
            return res.send('Not Found');
        res.send(metaTpl({
            title: (_a = rs.name) !== null && _a !== void 0 ? _a : '',
            desc: (_b = rs.overview) !== null && _b !== void 0 ? _b : '',
            keywords: '',
            url: `https://${req.hostname}/v2/unit/${id}`,
            image: rs.cover ? hashToUrl(rs.cover) : '',
        }));
    });
    app.get('/ssr/task/:id', async (req, res) => {
        var _a, _b;
        const ua = req.headers['user-agent'];
        const { id } = req.params;
        logger_1.default.info('ssr', req.url, req.hostname, req.protocol, id, ua);
        const rs = (await app
            .service('unit')
            .get(id, { query: { isLib: true } })
            .catch((e) => null)) || {};
        res.set('Content-Type', 'text/html');
        if (!rs)
            return res.send('Not Found');
        res.send(metaTpl({
            title: (_a = rs.name) !== null && _a !== void 0 ? _a : '',
            desc: (_b = rs.overview) !== null && _b !== void 0 ? _b : '',
            keywords: '',
            url: `https://${req.hostname}/v2/task/${id}`,
            image: rs.cover ? hashToUrl(rs.cover) : '',
        }));
    });
    app.get('/ssr/tool/:id', async (req, res) => {
        var _a, _b;
        const ua = req.headers['user-agent'];
        const { id } = req.params;
        logger_1.default.info('ssr', req.url, req.hostname, req.protocol, id, ua);
        const rs = (await app
            .service('unit')
            .get(id, { query: { isLib: true } })
            .catch((e) => null)) || {};
        res.set('Content-Type', 'text/html');
        if (!rs)
            return res.send('Not Found');
        res.send(metaTpl({
            title: (_a = rs.name) !== null && _a !== void 0 ? _a : '',
            desc: (_b = rs.overview) !== null && _b !== void 0 ? _b : '',
            keywords: '',
            url: `https://${req.hostname}/v2/task/${id}`,
            image: rs.cover ? hashToUrl(rs.cover) : '',
        }));
    });
    async function unit(req, res) {
        var _a, _b;
        const ua = req.headers['user-agent'];
        const { id } = req.params;
        logger_1.default.info('ssr', req.url, req.hostname, req.protocol, id, ua);
        const rs = (await app.service('unit').get(id, { query: { 'publish.lib': true } })) || {};
        res.set('Content-Type', 'text/html');
        if (!rs)
            return res.send('Not Found');
        res.send(metaTpl({
            title: (_a = rs.name) !== null && _a !== void 0 ? _a : '',
            desc: (_b = rs.overview) !== null && _b !== void 0 ? _b : '',
            keywords: '',
            url: `https://${req.hostname}${req.path.replace('/ssr/', '/v2/')}`,
            image: rs.cover ? hashToUrl(rs.cover) : '',
        }));
    }
    async function workshop(req, res) {
        var _a, _b;
        const ua = req.headers['user-agent'];
        const { id } = req.params;
        logger_1.default.info('ssr', req.url, req.hostname, req.protocol, id, ua);
        const rs = await app.service('session').Model.findById(id).select(['name', 'image']);
        res.set('Content-Type', 'text/html');
        if (!rs)
            return res.send('Not Found');
        res.send(metaTpl({
            title: (_a = rs.name) !== null && _a !== void 0 ? _a : '',
            desc: (_b = rs.goals) !== null && _b !== void 0 ? _b : '',
            keywords: '',
            url: `https://${req.hostname}${req.path.replace('/ssr/', '/v2/')}`,
            image: hashToUrl(rs.image),
        }));
    }
    app.get('/ssr/', async (req, res) => {
        res.set('Content-Type', 'text/html');
        const rs = await app.service('conf').get('website');
        res.send(metaTpl({ ...rs, image: `https://${req.hostname}/v2/logo.png`, url: `https://${req.hostname}/v2/` }));
    });
    app.get('/ssr/courses/:id', workshop);
    app.get('/ssr/session/:id', workshop);
    app.get('/ssr/workshop/:id', workshop);
    app.get('/ssr/pdCourses/:id', workshop);
    app.get('/ssr/taskWorkshop/:id', workshop);
    app.get('/ssr/workshop/:school/:id', workshop);
    // new
    app.get('/ssr/detail/lib/:id', unit);
    app.get('/ssr/detail/session/:id', workshop);
    app.get('/ssr/setting/postShare/:type/:id', async (req, res) => {
        const { id } = req.params;
        const doc = Acan.clone(await app.service('share-info').get(id));
        res.send(metaTpl(doc));
    });
}
exports.default = default_1;
